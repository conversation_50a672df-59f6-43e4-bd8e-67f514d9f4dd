using System;
using System.Drawing;

namespace AutoGame
{
    /// <summary>
    /// 存储解码后的帧数据
    /// </summary>
    public class FrameData
    {
        /// <summary>
        /// 帧的宽度
        /// </summary>
        public int Width { get; set; }

        /// <summary>
        /// 帧的高度
        /// </summary>
        public int Height { get; set; }

        /// <summary>
        /// RGB数据
        /// </summary>
        public byte[] RgbData { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 帧序号
        /// </summary>
        public long FrameNumber { get; set; }

        public FrameData(int width, int height, byte[] rgbData)
        {
            Width = width;
            Height = height;
            RgbData = rgbData;
            Timestamp = DateTime.Now;
        }

        /// <summary>
        /// 转换为Bitmap对象
        /// </summary>
        /// <returns></returns>
        public Bitmap ToBitmap()
        {
            if (RgbData == null || RgbData.Length == 0)
                return null;

            var bitmap = new Bitmap(Width, Height, System.Drawing.Imaging.PixelFormat.Format24bppRgb);
            var bitmapData = bitmap.LockBits(
                new Rectangle(0, 0, Width, Height),
                System.Drawing.Imaging.ImageLockMode.WriteOnly,
                System.Drawing.Imaging.PixelFormat.Format24bppRgb);

            try
            {
                System.Runtime.InteropServices.Marshal.Copy(RgbData, 0, bitmapData.Scan0, RgbData.Length);
            }
            finally
            {
                bitmap.UnlockBits(bitmapData);
            }

            return bitmap;
        }
    }
}
