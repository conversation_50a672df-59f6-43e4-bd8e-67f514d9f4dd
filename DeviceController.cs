using System;
using System.IO;
using System.Net.Sockets;
using System.Threading.Tasks;

namespace AutoGame
{
    /// <summary>
    /// 设备控制类，提供点击、滑动、息屏等操作
    /// </summary>
    public class DeviceController : IDisposable
    {
        private TcpClient _controlClient;
        private NetworkStream _controlStream;
        private DeviceInfo _deviceInfo;
        private bool _isConnected;

        /// <summary>
        /// 是否已连接
        /// </summary>
        public bool IsConnected => _isConnected && _controlClient?.Connected == true;

        /// <summary>
        /// 设备信息
        /// </summary>
        public DeviceInfo DeviceInfo => _deviceInfo;

        /// <summary>
        /// 连接到设备控制端口
        /// </summary>
        /// <param name="host">主机地址</param>
        /// <param name="port">控制端口</param>
        /// <param name="deviceInfo">设备信息</param>
        /// <returns>是否连接成功</returns>
        public async Task<bool> ConnectAsync(string host, int port, DeviceInfo deviceInfo)
        {
            try
            {
                _deviceInfo = deviceInfo;
                _controlClient = new TcpClient();
                await _controlClient.ConnectAsync(host, port);
                _controlStream = _controlClient.GetStream();
                _isConnected = true;
                
                Console.WriteLine($"Connected to device control port: {host}:{port}");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to connect to control port: {ex.Message}");
                Dispose();
                return false;
            }
        }

        /// <summary>
        /// 点击屏幕指定位置
        /// </summary>
        /// <param name="x">X坐标</param>
        /// <param name="y">Y坐标</param>
        /// <param name="duration">按压持续时间(毫秒)</param>
        /// <returns>是否成功</returns>
        public async Task<bool> ClickAsync(int x, int y, int duration = 100)
        {
            if (!IsConnected)
                return false;

            try
            {
                // 发送按下事件
                var downEvent = new TouchEvent(TouchEventType.Down, x, y);
                await SendTouchEventAsync(downEvent);

                // 等待指定时间
                if (duration > 0)
                {
                    await Task.Delay(duration);
                }

                // 发送抬起事件
                var upEvent = new TouchEvent(TouchEventType.Up, x, y);
                await SendTouchEventAsync(upEvent);

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to click: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 滑动操作
        /// </summary>
        /// <param name="startX">起始X坐标</param>
        /// <param name="startY">起始Y坐标</param>
        /// <param name="endX">结束X坐标</param>
        /// <param name="endY">结束Y坐标</param>
        /// <param name="duration">滑动持续时间(毫秒)</param>
        /// <param name="steps">滑动步数</param>
        /// <returns>是否成功</returns>
        public async Task<bool> SwipeAsync(int startX, int startY, int endX, int endY, int duration = 500, int steps = 10)
        {
            if (!IsConnected || steps <= 0)
                return false;

            try
            {
                // 发送按下事件
                var downEvent = new TouchEvent(TouchEventType.Down, startX, startY);
                await SendTouchEventAsync(downEvent);

                // 计算每步的移动距离和时间间隔
                var deltaX = (endX - startX) / (float)steps;
                var deltaY = (endY - startY) / (float)steps;
                var stepDelay = duration / steps;

                // 发送移动事件
                for (int i = 1; i <= steps; i++)
                {
                    var currentX = (int)(startX + deltaX * i);
                    var currentY = (int)(startY + deltaY * i);
                    
                    var moveEvent = new TouchEvent(TouchEventType.Move, currentX, currentY);
                    await SendTouchEventAsync(moveEvent);
                    
                    if (i < steps) // 最后一步不需要延迟
                    {
                        await Task.Delay(stepDelay);
                    }
                }

                // 发送抬起事件
                var upEvent = new TouchEvent(TouchEventType.Up, endX, endY);
                await SendTouchEventAsync(upEvent);

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to swipe: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 长按操作
        /// </summary>
        /// <param name="x">X坐标</param>
        /// <param name="y">Y坐标</param>
        /// <param name="duration">长按持续时间(毫秒)</param>
        /// <returns>是否成功</returns>
        public async Task<bool> LongPressAsync(int x, int y, int duration = 1000)
        {
            return await ClickAsync(x, y, duration);
        }

        /// <summary>
        /// 息屏/亮屏
        /// </summary>
        /// <param name="screenOn">true: 亮屏, false: 息屏</param>
        /// <returns>是否成功</returns>
        public async Task<bool> SetScreenPowerAsync(bool screenOn)
        {
            if (!IsConnected)
                return false;

            try
            {
                var message = ScrcpyProtocol.CreateScreenPowerMessage(screenOn);
                await _controlStream.WriteAsync(message, 0, message.Length);
                await _controlStream.FlushAsync();
                
                Console.WriteLine($"Screen power set to: {(screenOn ? "ON" : "OFF")}");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to set screen power: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 按下物理按键
        /// </summary>
        /// <param name="keyCode">按键代码</param>
        /// <returns>是否成功</returns>
        public async Task<bool> PressKeyAsync(int keyCode)
        {
            if (!IsConnected)
                return false;

            try
            {
                // 发送按下事件
                var downEvent = new KeyEvent(keyCode, true);
                var downMessage = ScrcpyProtocol.CreateKeyMessage(downEvent);
                await _controlStream.WriteAsync(downMessage, 0, downMessage.Length);

                // 短暂延迟
                await Task.Delay(50);

                // 发送释放事件
                var upEvent = new KeyEvent(keyCode, false);
                var upMessage = ScrcpyProtocol.CreateKeyMessage(upEvent);
                await _controlStream.WriteAsync(upMessage, 0, upMessage.Length);
                
                await _controlStream.FlushAsync();
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to press key: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 按下Home键
        /// </summary>
        public async Task<bool> PressHomeAsync()
        {
            return await PressKeyAsync(KeyCodes.KEYCODE_HOME);
        }

        /// <summary>
        /// 按下返回键
        /// </summary>
        public async Task<bool> PressBackAsync()
        {
            return await PressKeyAsync(KeyCodes.KEYCODE_BACK);
        }

        /// <summary>
        /// 按下电源键
        /// </summary>
        public async Task<bool> PressPowerAsync()
        {
            return await PressKeyAsync(KeyCodes.KEYCODE_POWER);
        }

        /// <summary>
        /// 按下菜单键
        /// </summary>
        public async Task<bool> PressMenuAsync()
        {
            return await PressKeyAsync(KeyCodes.KEYCODE_MENU);
        }

        /// <summary>
        /// 按下最近任务键
        /// </summary>
        public async Task<bool> PressAppSwitchAsync()
        {
            return await PressKeyAsync(KeyCodes.KEYCODE_APP_SWITCH);
        }

        /// <summary>
        /// 发送触摸事件
        /// </summary>
        private async Task SendTouchEventAsync(TouchEvent touchEvent)
        {
            if (!IsConnected || _deviceInfo == null)
                return;

            var message = ScrcpyProtocol.CreateTouchMessage(touchEvent, _deviceInfo.ScreenWidth, _deviceInfo.ScreenHeight);
            await _controlStream.WriteAsync(message, 0, message.Length);
            await _controlStream.FlushAsync();
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                _controlStream?.Close();
                _controlClient?.Close();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error disposing DeviceController: {ex.Message}");
            }
            finally
            {
                _controlStream = null;
                _controlClient = null;
                _isConnected = false;
            }
        }
    }
}
