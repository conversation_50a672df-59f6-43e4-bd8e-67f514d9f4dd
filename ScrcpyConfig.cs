using System;
using System.IO;

namespace AutoGame
{
    /// <summary>
    /// Scrcpy配置类
    /// </summary>
    public static class ScrcpyConfig
    {
        /// <summary>
        /// FFmpeg库路径
        /// </summary>
        public static string FFmpegPath { get; set; } = "ffmpeg";

        /// <summary>
        /// ADB可执行文件路径
        /// </summary>
        public static string AdbPath { get; set; } = "adb";

        /// <summary>
        /// Scrcpy服务器JAR文件路径
        /// </summary>
        public static string ServerJarPath { get; set; } = "/data/local/tmp/scrcpy-server.jar";

        /// <summary>
        /// 默认最大分辨率
        /// </summary>
        public static int DefaultMaxSize { get; set; } = 1920;

        /// <summary>
        /// 默认比特率
        /// </summary>
        public static int DefaultBitRate { get; set; } = 8000000;

        /// <summary>
        /// 默认最大帧率
        /// </summary>
        public static int DefaultMaxFps { get; set; } = 60;

        /// <summary>
        /// 连接超时时间（毫秒）
        /// </summary>
        public static int ConnectionTimeout { get; set; } = 10000;

        /// <summary>
        /// 视频流缓冲区大小
        /// </summary>
        public static int VideoBufferSize { get; set; } = 65536;

        /// <summary>
        /// 是否启用调试日志
        /// </summary>
        public static bool EnableDebugLog { get; set; } = false;

        /// <summary>
        /// 验证配置是否有效
        /// </summary>
        /// <returns>配置验证结果</returns>
        public static ConfigValidationResult ValidateConfig()
        {
            var result = new ConfigValidationResult();

            // 检查FFmpeg路径
            if (!Directory.Exists(FFmpegPath))
            {
                result.AddError($"FFmpeg路径不存在: {FFmpegPath}");
            }
            else
            {
                // 检查必要的FFmpeg库文件
                string[] requiredFiles = {
                    "avcodec-60.dll",
                    "avformat-60.dll", 
                    "avutil-58.dll",
                    "swresample-4.dll",
                    "swscale-7.dll"
                };

                foreach (var file in requiredFiles)
                {
                    var filePath = Path.Combine(FFmpegPath, file);
                    if (!File.Exists(filePath))
                    {
                        result.AddWarning($"FFmpeg库文件缺失: {file}");
                    }
                }
            }

            // 检查ADB
            try
            {
                var process = new System.Diagnostics.Process
                {
                    StartInfo = new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = AdbPath,
                        Arguments = "version",
                        UseShellExecute = false,
                        CreateNoWindow = true,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true
                    }
                };
                process.Start();
                process.WaitForExit(5000);
                
                if (process.ExitCode != 0)
                {
                    result.AddError("ADB不可用或版本检查失败");
                }
            }
            catch (Exception ex)
            {
                result.AddError($"无法执行ADB: {ex.Message}");
            }

            // 验证数值配置
            if (DefaultMaxSize <= 0)
            {
                result.AddError("最大分辨率必须大于0");
            }

            if (DefaultBitRate <= 0)
            {
                result.AddError("比特率必须大于0");
            }

            if (DefaultMaxFps <= 0)
            {
                result.AddError("最大帧率必须大于0");
            }

            if (ConnectionTimeout <= 0)
            {
                result.AddError("连接超时时间必须大于0");
            }

            return result;
        }

        /// <summary>
        /// 从配置文件加载设置
        /// </summary>
        /// <param name="configFilePath">配置文件路径</param>
        public static void LoadFromFile(string configFilePath = "scrcpy.config")
        {
            if (!File.Exists(configFilePath))
            {
                SaveToFile(configFilePath); // 创建默认配置文件
                return;
            }

            try
            {
                var lines = File.ReadAllLines(configFilePath);
                foreach (var line in lines)
                {
                    if (string.IsNullOrWhiteSpace(line) || line.StartsWith("#"))
                        continue;

                    var parts = line.Split('=');
                    if (parts.Length != 2)
                        continue;

                    var key = parts[0].Trim();
                    var value = parts[1].Trim();

                    switch (key.ToLower())
                    {
                        case "ffmpegpath":
                            FFmpegPath = value;
                            break;
                        case "adbpath":
                            AdbPath = value;
                            break;
                        case "serverjarpath":
                            ServerJarPath = value;
                            break;
                        case "defaultmaxsize":
                            if (int.TryParse(value, out int maxSize))
                                DefaultMaxSize = maxSize;
                            break;
                        case "defaultbitrate":
                            if (int.TryParse(value, out int bitRate))
                                DefaultBitRate = bitRate;
                            break;
                        case "defaultmaxfps":
                            if (int.TryParse(value, out int maxFps))
                                DefaultMaxFps = maxFps;
                            break;
                        case "connectiontimeout":
                            if (int.TryParse(value, out int timeout))
                                ConnectionTimeout = timeout;
                            break;
                        case "videobuffersize":
                            if (int.TryParse(value, out int bufferSize))
                                VideoBufferSize = bufferSize;
                            break;
                        case "enabledebuglog":
                            if (bool.TryParse(value, out bool enableLog))
                                EnableDebugLog = enableLog;
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"加载配置文件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 保存设置到配置文件
        /// </summary>
        /// <param name="configFilePath">配置文件路径</param>
        public static void SaveToFile(string configFilePath = "scrcpy.config")
        {
            try
            {
                var content = $@"# Scrcpy配置文件
# FFmpeg库路径
FFmpegPath={FFmpegPath}

# ADB可执行文件路径
AdbPath={AdbPath}

# Scrcpy服务器JAR文件路径
ServerJarPath={ServerJarPath}

# 默认最大分辨率
DefaultMaxSize={DefaultMaxSize}

# 默认比特率
DefaultBitRate={DefaultBitRate}

# 默认最大帧率
DefaultMaxFps={DefaultMaxFps}

# 连接超时时间（毫秒）
ConnectionTimeout={ConnectionTimeout}

# 视频流缓冲区大小
VideoBufferSize={VideoBufferSize}

# 是否启用调试日志
EnableDebugLog={EnableDebugLog}
";

                File.WriteAllText(configFilePath, content);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"保存配置文件失败: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// 配置验证结果
    /// </summary>
    public class ConfigValidationResult
    {
        public bool IsValid => Errors.Count == 0;
        public System.Collections.Generic.List<string> Errors { get; } = new System.Collections.Generic.List<string>();
        public System.Collections.Generic.List<string> Warnings { get; } = new System.Collections.Generic.List<string>();

        public void AddError(string error)
        {
            Errors.Add(error);
        }

        public void AddWarning(string warning)
        {
            Warnings.Add(warning);
        }

        public void PrintResults()
        {
            if (Errors.Count > 0)
            {
                Console.WriteLine("配置错误:");
                foreach (var error in Errors)
                {
                    Console.WriteLine($"  ❌ {error}");
                }
            }

            if (Warnings.Count > 0)
            {
                Console.WriteLine("配置警告:");
                foreach (var warning in Warnings)
                {
                    Console.WriteLine($"  ⚠️ {warning}");
                }
            }

            if (IsValid && Warnings.Count == 0)
            {
                Console.WriteLine("✅ 配置验证通过");
            }
        }
    }
}
