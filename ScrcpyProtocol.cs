using System;

namespace AutoGame
{
    /// <summary>
    /// Scrcpy协议相关常量和消息定义
    /// </summary>
    public static class ScrcpyProtocol
    {
        // 协议版本
        public const int PROTOCOL_VERSION = 2;

        // 服务器端口
        public const int DEFAULT_SERVER_PORT = 8080;
        public const int DEFAULT_CONTROL_PORT = 8081;

        // 消息类型
        public const byte MSG_TYPE_INJECT_KEYCODE = 0;
        public const byte MSG_TYPE_INJECT_TEXT = 1;
        public const byte MSG_TYPE_INJECT_TOUCH_EVENT = 2;
        public const byte MSG_TYPE_INJECT_SCROLL_EVENT = 3;
        public const byte MSG_TYPE_BACK_OR_SCREEN_ON = 4;
        public const byte MSG_TYPE_EXPAND_NOTIFICATION_PANEL = 5;
        public const byte MSG_TYPE_EXPAND_SETTINGS_PANEL = 6;
        public const byte MSG_TYPE_COLLAPSE_PANELS = 7;
        public const byte MSG_TYPE_GET_CLIPBOARD = 8;
        public const byte MSG_TYPE_SET_CLIPBOARD = 9;
        public const byte MSG_TYPE_SET_SCREEN_POWER_MODE = 10;
        public const byte MSG_TYPE_ROTATE_DEVICE = 11;

        // 屏幕电源模式
        public const byte SCREEN_POWER_MODE_OFF = 0;
        public const byte SCREEN_POWER_MODE_NORMAL = 2;

        // 触摸事件动作
        public const int ACTION_DOWN = 0;
        public const int ACTION_UP = 1;
        public const int ACTION_MOVE = 2;

        // 按钮状态
        public const int BUTTON_PRIMARY = 1;

        // 服务器JAR文件名
        public const string SERVER_JAR_NAME = "scrcpy-server.jar";

        // 默认配置
        public static class DefaultConfig
        {
            public const int MAX_SIZE = 1920;
            public const int BIT_RATE = 8000000;
            public const int MAX_FPS = 60;
            public const int LOCK_VIDEO_ORIENTATION = -1;
            public const bool TUNNEL_FORWARD = true;
            public const bool CROP_ENABLED = false;
            public const bool CONTROL_ENABLED = true;
            public const int DISPLAY_ID = 0;
            public const bool SHOW_TOUCHES = false;
            public const bool STAY_AWAKE = false;
            public const string CODEC_OPTIONS = "";
            public const string ENCODER_NAME = "";
        }

        /// <summary>
        /// 创建触摸事件消息
        /// </summary>
        public static byte[] CreateTouchMessage(TouchEvent touchEvent, int screenWidth, int screenHeight)
        {
            var message = new byte[32];
            var index = 0;

            // 消息类型
            message[index++] = MSG_TYPE_INJECT_TOUCH_EVENT;

            // 动作
            message[index++] = (byte)touchEvent.Type;

            // 指针ID
            WriteInt64(message, ref index, touchEvent.TouchId);

            // 位置 (归一化坐标)
            WriteInt32(message, ref index, touchEvent.X);
            WriteInt32(message, ref index, touchEvent.Y);

            // 屏幕尺寸
            WriteInt16(message, ref index, (short)screenWidth);
            WriteInt16(message, ref index, (short)screenHeight);

            // 压力
            WriteInt16(message, ref index, (short)(touchEvent.Pressure * 0xFFFF));

            // 按钮状态
            WriteInt32(message, ref index, touchEvent.Type == TouchEventType.Down || touchEvent.Type == TouchEventType.Move ? BUTTON_PRIMARY : 0);

            return message;
        }

        /// <summary>
        /// 创建按键事件消息
        /// </summary>
        public static byte[] CreateKeyMessage(KeyEvent keyEvent)
        {
            var message = new byte[14];
            var index = 0;

            // 消息类型
            message[index++] = MSG_TYPE_INJECT_KEYCODE;

            // 动作 (0: 按下, 1: 释放)
            message[index++] = (byte)(keyEvent.IsPressed ? 0 : 1);

            // 按键代码
            WriteInt32(message, ref index, keyEvent.KeyCode);

            // 重复次数
            WriteInt32(message, ref index, 0);

            // 元键状态
            WriteInt32(message, ref index, keyEvent.MetaState);

            return message;
        }

        /// <summary>
        /// 创建屏幕电源模式消息
        /// </summary>
        public static byte[] CreateScreenPowerMessage(bool screenOn)
        {
            var message = new byte[2];
            message[0] = MSG_TYPE_SET_SCREEN_POWER_MODE;
            message[1] = screenOn ? SCREEN_POWER_MODE_NORMAL : SCREEN_POWER_MODE_OFF;
            return message;
        }

        private static void WriteInt16(byte[] buffer, ref int index, short value)
        {
            buffer[index++] = (byte)(value >> 8);
            buffer[index++] = (byte)(value & 0xFF);
        }

        private static void WriteInt32(byte[] buffer, ref int index, int value)
        {
            buffer[index++] = (byte)(value >> 24);
            buffer[index++] = (byte)(value >> 16);
            buffer[index++] = (byte)(value >> 8);
            buffer[index++] = (byte)(value & 0xFF);
        }

        private static void WriteInt64(byte[] buffer, ref int index, long value)
        {
            buffer[index++] = (byte)(value >> 56);
            buffer[index++] = (byte)(value >> 48);
            buffer[index++] = (byte)(value >> 40);
            buffer[index++] = (byte)(value >> 32);
            buffer[index++] = (byte)(value >> 24);
            buffer[index++] = (byte)(value >> 16);
            buffer[index++] = (byte)(value >> 8);
            buffer[index++] = (byte)(value & 0xFF);
        }
    }
}
