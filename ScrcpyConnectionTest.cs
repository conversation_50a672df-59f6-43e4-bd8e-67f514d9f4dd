using System;
using System.Diagnostics;
using System.Threading.Tasks;

namespace AutoGame
{
    /// <summary>
    /// Scrcpy连接测试工具
    /// </summary>
    public static class ScrcpyConnectionTest
    {
        /// <summary>
        /// 测试完整的连接流程
        /// </summary>
        public static async Task TestFullConnectionAsync()
        {
            Console.WriteLine("=== Scrcpy连接测试 ===");
            Console.WriteLine();

            await TestDeviceDetectionAsync();
            await TestServerFileAsync();
            await TestPortForwardingAsync();
            await TestServerStartupAsync();
            
            Console.WriteLine();
            Console.WriteLine("=== 测试完成 ===");
        }

        /// <summary>
        /// 测试设备检测
        /// </summary>
        public static async Task TestDeviceDetectionAsync()
        {
            Console.WriteLine("1. 测试设备检测...");
            
            var devicesResult = await RunCommandAsync("adb", "devices -l");
            Console.WriteLine($"   设备列表: {devicesResult}");
            
            if (devicesResult.Contains("\tdevice"))
            {
                Console.WriteLine("   ✅ 检测到设备");
                
                // 获取第一个设备ID
                var lines = devicesResult.Split('\n');
                string deviceId = null;
                foreach (var line in lines)
                {
                    if (line.Contains("\tdevice"))
                    {
                        deviceId = line.Split('\t')[0];
                        break;
                    }
                }
                
                if (!string.IsNullOrEmpty(deviceId))
                {
                    Console.WriteLine($"   设备ID: {deviceId}");
                    
                    // 测试设备属性
                    var model = await RunCommandAsync("adb", $"-s {deviceId} shell getprop ro.product.model");
                    var version = await RunCommandAsync("adb", $"-s {deviceId} shell getprop ro.build.version.release");
                    var api = await RunCommandAsync("adb", $"-s {deviceId} shell getprop ro.build.version.sdk");
                    
                    Console.WriteLine($"   设备型号: {model.Trim()}");
                    Console.WriteLine($"   Android版本: {version.Trim()}");
                    Console.WriteLine($"   API级别: {api.Trim()}");
                }
            }
            else
            {
                Console.WriteLine("   ❌ 未检测到设备");
            }
            Console.WriteLine();
        }

        /// <summary>
        /// 测试服务器文件
        /// </summary>
        public static async Task TestServerFileAsync()
        {
            Console.WriteLine("2. 测试服务器文件...");
            
            // 检查本地文件
            if (System.IO.File.Exists("scrcpy-server.jar"))
            {
                var fileInfo = new System.IO.FileInfo("scrcpy-server.jar");
                Console.WriteLine($"   ✅ 本地文件存在 (大小: {fileInfo.Length} 字节)");
            }
            else
            {
                Console.WriteLine("   ❌ 本地scrcpy-server.jar不存在");
                return;
            }
            
            // 获取设备ID
            var devicesResult = await RunCommandAsync("adb", "devices");
            string deviceId = null;
            var lines = devicesResult.Split('\n');
            foreach (var line in lines)
            {
                if (line.Contains("\tdevice"))
                {
                    deviceId = line.Split('\t')[0];
                    break;
                }
            }
            
            if (string.IsNullOrEmpty(deviceId))
            {
                Console.WriteLine("   ❌ 未找到设备");
                return;
            }
            
            // 检查设备上的文件
            var serverCheck = await RunCommandAsync("adb", $"-s {deviceId} shell ls -l /data/local/tmp/scrcpy-server.jar");
            if (serverCheck.Contains("No such file") || serverCheck.Contains("not found"))
            {
                Console.WriteLine("   ❌ 设备上的服务器文件不存在，尝试推送...");
                var pushResult = await RunCommandAsync("adb", $"-s {deviceId} push scrcpy-server.jar /data/local/tmp/");
                Console.WriteLine($"   推送结果: {pushResult}");
            }
            else
            {
                Console.WriteLine($"   ✅ 设备上的服务器文件存在");
                Console.WriteLine($"   文件信息: {serverCheck.Trim()}");
            }
            Console.WriteLine();
        }

        /// <summary>
        /// 测试端口转发
        /// </summary>
        public static async Task TestPortForwardingAsync()
        {
            Console.WriteLine("3. 测试端口转发...");
            
            // 获取设备ID
            var devicesResult = await RunCommandAsync("adb", "devices");
            string deviceId = null;
            var lines = devicesResult.Split('\n');
            foreach (var line in lines)
            {
                if (line.Contains("\tdevice"))
                {
                    deviceId = line.Split('\t')[0];
                    break;
                }
            }
            
            if (string.IsNullOrEmpty(deviceId))
            {
                Console.WriteLine("   ❌ 未找到设备");
                return;
            }
            
            // 清理现有转发
            Console.WriteLine("   清理现有端口转发...");
            await RunCommandAsync("adb", $"-s {deviceId} forward --remove-all");
            
            // 设置端口转发
            Console.WriteLine("   设置端口转发...");
            var videoResult = await RunCommandAsync("adb", $"-s {deviceId} forward tcp:8080 tcp:8080");
            var controlResult = await RunCommandAsync("adb", $"-s {deviceId} forward tcp:8081 tcp:8081");
            
            Console.WriteLine($"   视频端口转发: {(string.IsNullOrEmpty(videoResult.Trim()) ? "✅ 成功" : $"❌ {videoResult}")}");
            Console.WriteLine($"   控制端口转发: {(string.IsNullOrEmpty(controlResult.Trim()) ? "✅ 成功" : $"❌ {controlResult}")}");
            
            // 验证端口转发
            var listResult = await RunCommandAsync("adb", $"-s {deviceId} forward --list");
            Console.WriteLine($"   当前转发列表: {listResult}");
            Console.WriteLine();
        }

        /// <summary>
        /// 测试服务器启动
        /// </summary>
        public static async Task TestServerStartupAsync()
        {
            Console.WriteLine("4. 测试服务器启动...");
            
            // 获取设备ID
            var devicesResult = await RunCommandAsync("adb", "devices");
            string deviceId = null;
            var lines = devicesResult.Split('\n');
            foreach (var line in lines)
            {
                if (line.Contains("\tdevice"))
                {
                    deviceId = line.Split('\t')[0];
                    break;
                }
            }
            
            if (string.IsNullOrEmpty(deviceId))
            {
                Console.WriteLine("   ❌ 未找到设备");
                return;
            }
            
            // 构建服务器启动命令 (使用新版本格式)
            var scid = DateTime.Now.Ticks.ToString("X8").Substring(0, 8);
            var serverArgs = $"video_bit_rate=2000000 log_level=info max_size=1080 capture_orientation=0 audio=false scid={scid}";
            var command = $"-s {deviceId} shell CLASSPATH=/data/local/tmp/scrcpy-server.jar app_process / com.genymobile.scrcpy.Server 3.1 {serverArgs}";
            
            Console.WriteLine($"   启动命令: adb {command}");
            Console.WriteLine("   正在启动服务器...");
            
            // 启动服务器进程
            var process = new Process
            {
                StartInfo = new ProcessStartInfo
                {
                    FileName = "adb",
                    Arguments = command,
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                }
            };
            
            process.Start();
            
            // 等待一段时间检查输出
            await Task.Delay(3000);
            
            if (!process.HasExited)
            {
                Console.WriteLine("   ✅ 服务器进程正在运行");
                
                // 尝试读取输出
                try
                {
                    var output = await process.StandardOutput.ReadToEndAsync();
                    var error = await process.StandardError.ReadToEndAsync();
                    
                    if (!string.IsNullOrEmpty(output))
                    {
                        Console.WriteLine($"   标准输出: {output}");
                    }
                    
                    if (!string.IsNullOrEmpty(error))
                    {
                        Console.WriteLine($"   错误输出: {error}");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   读取输出失败: {ex.Message}");
                }
                
                // 停止进程
                try
                {
                    process.Kill();
                    Console.WriteLine("   已停止测试服务器");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   停止服务器失败: {ex.Message}");
                }
            }
            else
            {
                Console.WriteLine($"   ❌ 服务器进程已退出 (退出代码: {process.ExitCode})");
                
                try
                {
                    var error = await process.StandardError.ReadToEndAsync();
                    if (!string.IsNullOrEmpty(error))
                    {
                        Console.WriteLine($"   错误输出: {error}");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   读取错误输出失败: {ex.Message}");
                }
            }
            
            Console.WriteLine();
        }

        /// <summary>
        /// 运行命令并返回输出
        /// </summary>
        private static async Task<string> RunCommandAsync(string fileName, string arguments)
        {
            try
            {
                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = fileName,
                        Arguments = arguments,
                        UseShellExecute = false,
                        CreateNoWindow = true,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true
                    }
                };

                process.Start();
                var output = await process.StandardOutput.ReadToEndAsync();
                var error = await process.StandardError.ReadToEndAsync();
                process.WaitForExit();

                if (!string.IsNullOrEmpty(error))
                {
                    return $"ERROR: {error}";
                }

                return output;
            }
            catch (Exception ex)
            {
                return $"EXCEPTION: {ex.Message}";
            }
        }

        /// <summary>
        /// 快速连接测试
        /// </summary>
        public static async Task QuickConnectionTestAsync()
        {
            Console.WriteLine("=== 快速连接测试 ===");
            
            try
            {
                var client = new ScrcpyClient();
                Console.WriteLine("正在尝试连接...");
                
                bool connected = await client.ConnectAsync(null, 1080, 2000000);
                
                if (connected)
                {
                    Console.WriteLine("✅ 连接成功！");
                    
                    // 等待一下获取视频帧
                    await Task.Delay(2000);
                    
                    var frame = client.GetLatestFrame();
                    if (frame != null)
                    {
                        Console.WriteLine($"✅ 获取到视频帧: {frame.Width}x{frame.Height}");
                    }
                    else
                    {
                        Console.WriteLine("⚠️ 未获取到视频帧");
                    }
                    
                    // 测试设备控制
                    if (client.DeviceController.IsConnected)
                    {
                        Console.WriteLine("✅ 设备控制器已连接");
                    }
                    else
                    {
                        Console.WriteLine("❌ 设备控制器未连接");
                    }
                    
                    await client.DisconnectAsync();
                    Console.WriteLine("✅ 已断开连接");
                }
                else
                {
                    Console.WriteLine("❌ 连接失败");
                }
                
                client.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试异常: {ex.Message}");
            }
            
            Console.WriteLine();
        }
    }
}
