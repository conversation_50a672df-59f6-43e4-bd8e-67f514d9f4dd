# AutoGame 使用示例

## 基本使用

### 1. 连接设备并获取屏幕截图

```csharp
using AutoGame;

class Program
{
    static async Task Main(string[] args)
    {
        // 加载配置
        ScrcpyConfig.LoadFromFile();
        
        var client = new ScrcpyClient();
        
        try
        {
            // 连接设备
            bool connected = await client.ConnectAsync();
            if (!connected)
            {
                Console.WriteLine("连接失败");
                return;
            }
            
            // 等待视频流稳定
            await Task.Delay(2000);
            
            // 获取最新帧并保存为图片
            var frame = client.GetLatestFrame();
            if (frame != null)
            {
                var bitmap = frame.ToBitmap();
                bitmap.Save("screenshot.png");
                Console.WriteLine("截图已保存");
                bitmap.Dispose();
            }
        }
        finally
        {
            await client.DisconnectAsync();
            client.Dispose();
        }
    }
}
```

### 2. 自动点击操作

```csharp
// 连接设备后...
var controller = client.DeviceController;

// 点击屏幕中心
int centerX = client.DeviceInfo.ScreenWidth / 2;
int centerY = client.DeviceInfo.ScreenHeight / 2;
await controller.ClickAsync(centerX, centerY);

// 长按操作
await controller.LongPressAsync(centerX, centerY, 1000);

// 连续点击
for (int i = 0; i < 5; i++)
{
    await controller.ClickAsync(100 + i * 50, 200);
    await Task.Delay(500);
}
```

### 3. 滑动操作

```csharp
var controller = client.DeviceController;
var deviceInfo = client.DeviceInfo;

// 向上滑动（模拟滚动）
await controller.SwipeAsync(
    deviceInfo.ScreenWidth / 2, deviceInfo.ScreenHeight * 3 / 4,
    deviceInfo.ScreenWidth / 2, deviceInfo.ScreenHeight / 4,
    800);

// 向右滑动
await controller.SwipeAsync(
    100, deviceInfo.ScreenHeight / 2,
    deviceInfo.ScreenWidth - 100, deviceInfo.ScreenHeight / 2,
    500);

// 对角线滑动
await controller.SwipeAsync(0, 0, deviceInfo.ScreenWidth, deviceInfo.ScreenHeight, 1000);
```

### 4. 物理按键操作

```csharp
var controller = client.DeviceController;

// 按下Home键
await controller.PressHomeAsync();

// 按下返回键
await controller.PressBackAsync();

// 按下电源键
await controller.PressPowerAsync();

// 按下菜单键
await controller.PressMenuAsync();

// 打开最近任务
await controller.PressAppSwitchAsync();

// 息屏和亮屏
await controller.SetScreenPowerAsync(false); // 息屏
await Task.Delay(2000);
await controller.SetScreenPowerAsync(true);  // 亮屏
```

## 游戏自动化示例

### 1. 简单的点击游戏自动化

```csharp
public class ClickGameBot
{
    private ScrcpyClient _client;
    private bool _isRunning;
    
    public async Task<bool> StartAsync()
    {
        _client = new ScrcpyClient();
        bool connected = await _client.ConnectAsync();
        
        if (connected)
        {
            Console.WriteLine($"已连接设备: {_client.DeviceInfo}");
            return true;
        }
        return false;
    }
    
    public async Task RunAutoClickAsync()
    {
        _isRunning = true;
        var controller = _client.DeviceController;
        var deviceInfo = _client.DeviceInfo;
        
        // 游戏区域（假设游戏在屏幕中央区域）
        int gameLeft = deviceInfo.ScreenWidth / 4;
        int gameRight = deviceInfo.ScreenWidth * 3 / 4;
        int gameTop = deviceInfo.ScreenHeight / 4;
        int gameBottom = deviceInfo.ScreenHeight * 3 / 4;
        
        Random random = new Random();
        
        while (_isRunning)
        {
            // 随机点击游戏区域
            int x = random.Next(gameLeft, gameRight);
            int y = random.Next(gameTop, gameBottom);
            
            await controller.ClickAsync(x, y);
            Console.WriteLine($"点击了 ({x}, {y})");
            
            // 随机等待时间
            await Task.Delay(random.Next(500, 2000));
        }
    }
    
    public void Stop()
    {
        _isRunning = false;
    }
    
    public async Task CleanupAsync()
    {
        if (_client != null)
        {
            await _client.DisconnectAsync();
            _client.Dispose();
        }
    }
}
```

### 2. 基于图像识别的自动化

```csharp
public class ImageBasedBot
{
    private ScrcpyClient _client;
    
    public async Task<bool> FindAndClickButtonAsync(string buttonImagePath)
    {
        // 获取当前屏幕
        var frame = _client.GetLatestFrame();
        if (frame == null) return false;
        
        var screenBitmap = frame.ToBitmap();
        
        try
        {
            // 这里可以使用OpenCV或其他图像处理库进行模板匹配
            // 简化示例：假设我们有一个方法来查找按钮位置
            var buttonLocation = FindButtonInImage(screenBitmap, buttonImagePath);
            
            if (buttonLocation.HasValue)
            {
                await _client.DeviceController.ClickAsync(
                    buttonLocation.Value.X, 
                    buttonLocation.Value.Y);
                return true;
            }
        }
        finally
        {
            screenBitmap.Dispose();
        }
        
        return false;
    }
    
    private Point? FindButtonInImage(Bitmap screen, string buttonImagePath)
    {
        // 这里应该实现实际的图像匹配逻辑
        // 可以使用OpenCvSharp或其他图像处理库
        // 返回找到的按钮中心坐标
        return null; // 占位符
    }
}
```

### 3. 游戏状态监控

```csharp
public class GameStateMonitor
{
    private ScrcpyClient _client;
    private bool _isMonitoring;
    
    public async Task StartMonitoringAsync()
    {
        _isMonitoring = true;
        
        while (_isMonitoring)
        {
            var frame = _client.GetLatestFrame();
            if (frame != null)
            {
                // 分析游戏状态
                var gameState = AnalyzeGameState(frame);
                
                // 根据状态执行相应操作
                await HandleGameState(gameState);
            }
            
            await Task.Delay(100); // 每100ms检查一次
        }
    }
    
    private GameState AnalyzeGameState(FrameData frame)
    {
        // 这里实现游戏状态分析逻辑
        // 可以检查特定区域的颜色、文字等
        
        var bitmap = frame.ToBitmap();
        try
        {
            // 示例：检查特定位置的颜色来判断游戏状态
            var pixel = bitmap.GetPixel(100, 100);
            
            if (pixel.R > 200 && pixel.G < 50 && pixel.B < 50)
            {
                return GameState.GameOver;
            }
            else if (pixel.G > 200 && pixel.R < 50 && pixel.B < 50)
            {
                return GameState.Victory;
            }
            
            return GameState.Playing;
        }
        finally
        {
            bitmap.Dispose();
        }
    }
    
    private async Task HandleGameState(GameState state)
    {
        switch (state)
        {
            case GameState.GameOver:
                Console.WriteLine("游戏结束，重新开始");
                await _client.DeviceController.ClickAsync(500, 600); // 重新开始按钮
                break;
                
            case GameState.Victory:
                Console.WriteLine("胜利！继续下一关");
                await _client.DeviceController.ClickAsync(500, 700); // 下一关按钮
                break;
                
            case GameState.Playing:
                // 继续游戏逻辑
                break;
        }
    }
}

public enum GameState
{
    Playing,
    GameOver,
    Victory,
    Menu
}
```

## 实用工具函数

### 1. 屏幕录制

```csharp
public class ScreenRecorder
{
    private ScrcpyClient _client;
    private List<FrameData> _frames = new List<FrameData>();
    private bool _isRecording;
    
    public async Task StartRecordingAsync()
    {
        _isRecording = true;
        _frames.Clear();
        
        while (_isRecording)
        {
            var frame = _client.GetLatestFrame();
            if (frame != null)
            {
                _frames.Add(frame);
            }
            
            await Task.Delay(33); // 约30fps
        }
    }
    
    public void StopRecording()
    {
        _isRecording = false;
    }
    
    public void SaveFramesAsImages(string directory)
    {
        Directory.CreateDirectory(directory);
        
        for (int i = 0; i < _frames.Count; i++)
        {
            var bitmap = _frames[i].ToBitmap();
            bitmap.Save(Path.Combine(directory, $"frame_{i:D6}.png"));
            bitmap.Dispose();
        }
    }
}
```

### 2. 性能监控

```csharp
public class PerformanceMonitor
{
    private ScrcpyClient _client;
    private DateTime _lastFrameTime;
    private long _lastFrameNumber;
    
    public void StartMonitoring()
    {
        Task.Run(async () =>
        {
            while (true)
            {
                var frame = _client.GetLatestFrame();
                if (frame != null && frame.FrameNumber != _lastFrameNumber)
                {
                    var now = DateTime.Now;
                    if (_lastFrameTime != default)
                    {
                        var fps = 1000.0 / (now - _lastFrameTime).TotalMilliseconds;
                        Console.WriteLine($"FPS: {fps:F1}, Frame: {frame.FrameNumber}");
                    }
                    
                    _lastFrameTime = now;
                    _lastFrameNumber = frame.FrameNumber;
                }
                
                await Task.Delay(100);
            }
        });
    }
}
```

## 注意事项

1. **性能优化**: 频繁的图像处理会消耗大量CPU，建议适当降低检查频率
2. **内存管理**: 及时释放Bitmap对象避免内存泄漏
3. **异常处理**: 网络连接可能不稳定，需要适当的重连机制
4. **设备兼容性**: 不同设备的屏幕分辨率和性能差异较大，需要适配
5. **权限问题**: 某些操作可能需要root权限或特殊权限
