@echo off
chcp 65001 >nul
echo ========================================
echo AutoGame - Scrcpy 环境设置脚本
echo ========================================
echo.

echo 正在检查环境...

:: 检查ADB
echo 检查ADB...
adb version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ADB未找到，请安装Android SDK Platform Tools
    echo    下载地址: https://developer.android.com/studio/releases/platform-tools
    pause
    exit /b 1
) else (
    echo ✅ ADB已安装
)

:: 创建FFmpeg目录
echo.
echo 创建FFmpeg目录...
if not exist "ffmpeg" (
    mkdir ffmpeg
    echo ✅ 已创建ffmpeg目录
) else (
    echo ✅ ffmpeg目录已存在
)

:: 检查FFmpeg库文件
echo.
echo 检查FFmpeg库文件...
set "missing_files="
if not exist "ffmpeg\avcodec-60.dll" set "missing_files=%missing_files% avcodec-60.dll"
if not exist "ffmpeg\avformat-60.dll" set "missing_files=%missing_files% avformat-60.dll"
if not exist "ffmpeg\avutil-58.dll" set "missing_files=%missing_files% avutil-58.dll"
if not exist "ffmpeg\swresample-4.dll" set "missing_files=%missing_files% swresample-4.dll"
if not exist "ffmpeg\swscale-7.dll" set "missing_files=%missing_files% swscale-7.dll"

if defined missing_files (
    echo ❌ 缺少FFmpeg库文件:%missing_files%
    echo    请从以下地址下载FFmpeg库文件并放入ffmpeg目录:
    echo    https://github.com/BtbN/FFmpeg-Builds/releases
    echo    选择 ffmpeg-master-latest-win64-gpl-shared.zip
) else (
    echo ✅ FFmpeg库文件完整
)

:: 检查scrcpy-server.jar
echo.
echo 检查scrcpy-server.jar...
if not exist "scrcpy-server.jar" (
    echo ❌ 未找到scrcpy-server.jar
    echo    请从以下地址下载scrcpy-server.jar:
    echo    https://github.com/Genymobile/scrcpy/releases
    echo    下载后放在项目根目录
) else (
    echo ✅ scrcpy-server.jar已存在
)

:: 推送scrcpy-server.jar到设备
echo.
echo 检查Android设备连接...
adb devices | findstr "device$" >nul
if %errorlevel% neq 0 (
    echo ⚠️ 未检测到Android设备
    echo    请确保:
    echo    1. 设备已通过USB连接
    echo    2. 已启用USB调试
    echo    3. 已授权此计算机进行调试
) else (
    echo ✅ 检测到Android设备
    
    if exist "scrcpy-server.jar" (
        echo 正在推送scrcpy-server.jar到设备...
        adb push scrcpy-server.jar /data/local/tmp/
        if %errorlevel% equ 0 (
            echo ✅ scrcpy-server.jar推送成功
        ) else (
            echo ❌ scrcpy-server.jar推送失败
        )
    )
)

:: 生成配置文件
echo.
echo 生成配置文件...
if not exist "scrcpy.config" (
    echo # Scrcpy配置文件 > scrcpy.config
    echo # FFmpeg库路径 >> scrcpy.config
    echo FFmpegPath=ffmpeg >> scrcpy.config
    echo. >> scrcpy.config
    echo # ADB可执行文件路径 >> scrcpy.config
    echo AdbPath=adb >> scrcpy.config
    echo. >> scrcpy.config
    echo # Scrcpy服务器JAR文件路径 >> scrcpy.config
    echo ServerJarPath=/data/local/tmp/scrcpy-server.jar >> scrcpy.config
    echo. >> scrcpy.config
    echo # 默认最大分辨率 >> scrcpy.config
    echo DefaultMaxSize=1920 >> scrcpy.config
    echo. >> scrcpy.config
    echo # 默认比特率 >> scrcpy.config
    echo DefaultBitRate=8000000 >> scrcpy.config
    echo. >> scrcpy.config
    echo # 默认最大帧率 >> scrcpy.config
    echo DefaultMaxFps=60 >> scrcpy.config
    echo. >> scrcpy.config
    echo # 连接超时时间（毫秒） >> scrcpy.config
    echo ConnectionTimeout=10000 >> scrcpy.config
    echo. >> scrcpy.config
    echo # 视频流缓冲区大小 >> scrcpy.config
    echo VideoBufferSize=65536 >> scrcpy.config
    echo. >> scrcpy.config
    echo # 是否启用调试日志 >> scrcpy.config
    echo EnableDebugLog=false >> scrcpy.config
    
    echo ✅ 已生成默认配置文件 scrcpy.config
) else (
    echo ✅ 配置文件已存在
)

echo.
echo ========================================
echo 环境设置完成！
echo ========================================
echo.
echo 下一步:
echo 1. 如果缺少FFmpeg库文件，请下载并放入ffmpeg目录
echo 2. 如果缺少scrcpy-server.jar，请下载并放在项目根目录
echo 3. 连接Android设备并启用USB调试
echo 4. 编译并运行AutoGame项目
echo.
pause
