using System;

namespace AutoGame
{
    /// <summary>
    /// 触摸事件类型
    /// </summary>
    public enum TouchEventType
    {
        Down = 0,
        Up = 1,
        Move = 2
    }

    /// <summary>
    /// 触摸事件数据结构
    /// </summary>
    public class TouchEvent
    {
        /// <summary>
        /// 事件类型
        /// </summary>
        public TouchEventType Type { get; set; }

        /// <summary>
        /// X坐标
        /// </summary>
        public int X { get; set; }

        /// <summary>
        /// Y坐标
        /// </summary>
        public int Y { get; set; }

        /// <summary>
        /// 压力值 (0.0 - 1.0)
        /// </summary>
        public float Pressure { get; set; }

        /// <summary>
        /// 触摸ID
        /// </summary>
        public int TouchId { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public long Timestamp { get; set; }

        public TouchEvent(TouchEventType type, int x, int y, float pressure = 1.0f, int touchId = 0)
        {
            Type = type;
            X = x;
            Y = y;
            Pressure = pressure;
            TouchId = touchId;
            Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
        }
    }

    /// <summary>
    /// 按键事件
    /// </summary>
    public class KeyEvent
    {
        /// <summary>
        /// 按键代码
        /// </summary>
        public int KeyCode { get; set; }

        /// <summary>
        /// 是否按下 (true: 按下, false: 释放)
        /// </summary>
        public bool IsPressed { get; set; }

        /// <summary>
        /// 元键状态
        /// </summary>
        public int MetaState { get; set; }

        public KeyEvent(int keyCode, bool isPressed, int metaState = 0)
        {
            KeyCode = keyCode;
            IsPressed = isPressed;
            MetaState = metaState;
        }
    }

    /// <summary>
    /// 常用按键代码
    /// </summary>
    public static class KeyCodes
    {
        public const int KEYCODE_POWER = 26;
        public const int KEYCODE_HOME = 3;
        public const int KEYCODE_BACK = 4;
        public const int KEYCODE_MENU = 82;
        public const int KEYCODE_VOLUME_UP = 24;
        public const int KEYCODE_VOLUME_DOWN = 25;
        public const int KEYCODE_APP_SWITCH = 187;
    }
}
