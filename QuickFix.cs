using System;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;

namespace AutoGame
{
    /// <summary>
    /// 快速修复工具，用于自动解决常见问题
    /// </summary>
    public static class QuickFix
    {
        /// <summary>
        /// 运行快速修复
        /// </summary>
        public static async Task RunQuickFixAsync()
        {
            Console.WriteLine("=== AutoGame 快速修复工具 ===");
            Console.WriteLine();

            await FixPortForwardingAsync();
            await FixScrcpyServerAsync();
            await FixPermissionsAsync();
            
            Console.WriteLine();
            Console.WriteLine("=== 快速修复完成 ===");
        }

        /// <summary>
        /// 修复端口转发问题
        /// </summary>
        public static async Task FixPortForwardingAsync()
        {
            Console.WriteLine("1. 修复端口转发问题...");
            
            try
            {
                // 清理所有端口转发
                var result = await RunCommandAsync("adb", "forward --remove-all");
                Console.WriteLine("   ✅ 已清理所有端口转发");
                
                // 重启ADB服务器
                await RunCommandAsync("adb", "kill-server");
                await Task.Delay(1000);
                await RunCommandAsync("adb", "start-server");
                Console.WriteLine("   ✅ 已重启ADB服务器");
                
                // 检查设备连接
                var devices = await RunCommandAsync("adb", "devices");
                if (devices.Contains("\tdevice"))
                {
                    Console.WriteLine("   ✅ 设备连接正常");
                }
                else
                {
                    Console.WriteLine("   ⚠️ 请检查设备连接");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 端口转发修复失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 修复scrcpy服务器问题
        /// </summary>
        public static async Task FixScrcpyServerAsync()
        {
            Console.WriteLine("\n2. 修复scrcpy服务器问题...");
            
            try
            {
                // 检查本地文件
                if (!File.Exists("scrcpy-server.jar"))
                {
                    Console.WriteLine("   ❌ 本地scrcpy-server.jar文件不存在");
                    Console.WriteLine("   请从 https://github.com/Genymobile/scrcpy/releases 下载");
                    return;
                }
                
                // 停止可能运行的服务器进程
                await RunCommandAsync("adb", "shell pkill -f scrcpy");
                Console.WriteLine("   ✅ 已停止现有服务器进程");
                
                // 重新推送服务器文件
                var pushResult = await RunCommandAsync("adb", "push scrcpy-server.jar /data/local/tmp/");
                if (pushResult.Contains("1 file pushed") || pushResult.Contains("pushed"))
                {
                    Console.WriteLine("   ✅ 服务器文件推送成功");
                }
                else
                {
                    Console.WriteLine($"   ❌ 服务器文件推送失败: {pushResult}");
                }
                
                // 设置文件权限
                await RunCommandAsync("adb", "shell chmod 755 /data/local/tmp/scrcpy-server.jar");
                Console.WriteLine("   ✅ 已设置文件权限");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 服务器修复失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 修复权限问题
        /// </summary>
        public static async Task FixPermissionsAsync()
        {
            Console.WriteLine("\n3. 修复权限问题...");
            
            try
            {
                // 检查USB调试状态
                var usbDebugging = await RunCommandAsync("adb", "shell getprop ro.debuggable");
                if (usbDebugging.Trim() == "1")
                {
                    Console.WriteLine("   ✅ USB调试已启用");
                }
                else
                {
                    Console.WriteLine("   ⚠️ 请确保USB调试已启用");
                }
                
                // 检查开发者选项
                var devOptions = await RunCommandAsync("adb", "shell getprop ro.build.type");
                Console.WriteLine($"   构建类型: {devOptions.Trim()}");
                
                // 尝试获取屏幕权限（测试）
                var screenTest = await RunCommandAsync("adb", "shell screencap -p > /dev/null");
                if (string.IsNullOrEmpty(screenTest) || !screenTest.Contains("Permission denied"))
                {
                    Console.WriteLine("   ✅ 屏幕捕获权限正常");
                }
                else
                {
                    Console.WriteLine("   ❌ 屏幕捕获权限不足");
                    Console.WriteLine("   请在设备上授权此计算机进行调试");
                }
                
                // 检查存储权限
                var storageTest = await RunCommandAsync("adb", "shell ls /data/local/tmp/");
                if (!storageTest.Contains("Permission denied"))
                {
                    Console.WriteLine("   ✅ 存储访问权限正常");
                }
                else
                {
                    Console.WriteLine("   ❌ 存储访问权限不足");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 权限检查失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 修复网络连接问题
        /// </summary>
        public static async Task FixNetworkAsync()
        {
            Console.WriteLine("\n4. 修复网络连接问题...");
            
            try
            {
                // 检查本地端口占用
                var netstat = await RunCommandAsync("netstat", "-an | findstr :8080");
                if (!string.IsNullOrEmpty(netstat))
                {
                    Console.WriteLine("   ⚠️ 端口8080被占用");
                    Console.WriteLine("   尝试释放端口...");
                    
                    // 尝试找到占用进程并终止
                    var processInfo = await RunCommandAsync("netstat", "-ano | findstr :8080");
                    Console.WriteLine($"   端口占用信息: {processInfo}");
                }
                else
                {
                    Console.WriteLine("   ✅ 端口8080可用");
                }
                
                // 检查防火墙设置
                Console.WriteLine("   请确保防火墙允许端口8080和8081的连接");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 网络检查失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 重置所有设置
        /// </summary>
        public static async Task ResetAllAsync()
        {
            Console.WriteLine("\n=== 重置所有设置 ===");
            
            try
            {
                // 停止所有相关进程
                await RunCommandAsync("adb", "kill-server");
                await RunCommandAsync("taskkill", "/f /im adb.exe");
                
                Console.WriteLine("   ✅ 已停止ADB进程");
                
                // 清理端口转发
                await Task.Delay(2000);
                await RunCommandAsync("adb", "start-server");
                await RunCommandAsync("adb", "forward --remove-all");
                
                Console.WriteLine("   ✅ 已清理端口转发");
                
                // 停止设备上的服务器
                await RunCommandAsync("adb", "shell pkill -f scrcpy");
                await RunCommandAsync("adb", "shell pkill -f app_process");
                
                Console.WriteLine("   ✅ 已停止设备服务器");
                
                // 重新推送服务器文件
                if (File.Exists("scrcpy-server.jar"))
                {
                    await RunCommandAsync("adb", "push scrcpy-server.jar /data/local/tmp/");
                    Console.WriteLine("   ✅ 已重新推送服务器文件");
                }
                
                Console.WriteLine("\n重置完成，请重新运行程序");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 重置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 自动修复常见问题
        /// </summary>
        public static async Task AutoFixAsync()
        {
            Console.WriteLine("=== 自动修复模式 ===");
            Console.WriteLine("正在检测并修复常见问题...");
            
            // 运行诊断
            await DiagnosticTool.RunFullDiagnosticAsync();
            
            Console.WriteLine("\n开始自动修复...");
            
            // 执行修复步骤
            await FixPortForwardingAsync();
            await FixScrcpyServerAsync();
            await FixPermissionsAsync();
            await FixNetworkAsync();
            
            Console.WriteLine("\n自动修复完成！");
            Console.WriteLine("如果问题仍然存在，请尝试:");
            Console.WriteLine("1. 重启设备");
            Console.WriteLine("2. 更换USB线缆");
            Console.WriteLine("3. 使用不同的USB端口");
            Console.WriteLine("4. 检查设备驱动程序");
        }

        /// <summary>
        /// 运行命令并返回输出
        /// </summary>
        private static async Task<string> RunCommandAsync(string fileName, string arguments)
        {
            try
            {
                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = fileName,
                        Arguments = arguments,
                        UseShellExecute = false,
                        CreateNoWindow = true,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true
                    }
                };

                process.Start();
                var output = await process.StandardOutput.ReadToEndAsync();
                var error = await process.StandardError.ReadToEndAsync();
                process.WaitForExit();

                return string.IsNullOrEmpty(error) ? output : error;
            }
            catch (Exception)
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// 交互式修复向导
        /// </summary>
        public static async Task RunFixWizardAsync()
        {
            Console.WriteLine("=== AutoGame 修复向导 ===");
            Console.WriteLine();
            Console.WriteLine("请选择修复选项:");
            Console.WriteLine("1. 快速修复 (推荐)");
            Console.WriteLine("2. 重置所有设置");
            Console.WriteLine("3. 自动修复");
            Console.WriteLine("4. 退出");
            
            Console.Write("\n请输入选项 (1-4): ");
            var choice = Console.ReadLine();
            
            switch (choice)
            {
                case "1":
                    await RunQuickFixAsync();
                    break;
                case "2":
                    await ResetAllAsync();
                    break;
                case "3":
                    await AutoFixAsync();
                    break;
                case "4":
                    return;
                default:
                    Console.WriteLine("无效选项");
                    break;
            }
            
            Console.WriteLine("\n按任意键继续...");
            Console.ReadKey();
        }
    }
}
