using System;
using System.Runtime.InteropServices;
using System.Threading;
using FFmpeg.AutoGen;

namespace AutoGame
{
    /// <summary>
    /// 使用FFmpeg.AutoGen解码视频流的类
    /// </summary>
    public unsafe class VideoDecoder : IDisposable
    {
        private AVCodecContext* _codecContext;
        private AVCodec* _codec;
        private AVFrame* _frame;
        private AVFrame* _frameRgb;
        private SwsContext* _swsContext;
        private byte* _buffer;
        private int _bufferSize;
        private bool _isInitialized;
        private readonly object _lockObject = new object();
        private FrameData _latestFrame;
        private long _frameNumber;

        /// <summary>
        /// 获取最新的帧数据
        /// </summary>
        public FrameData LatestFrame
        {
            get
            {
                lock (_lockObject)
                {
                    return _latestFrame;
                }
            }
        }

        /// <summary>
        /// 是否已初始化
        /// </summary>
        public bool IsInitialized => _isInitialized;

        /// <summary>
        /// 视频宽度
        /// </summary>
        public int Width { get; private set; }

        /// <summary>
        /// 视频高度
        /// </summary>
        public int Height { get; private set; }

        static VideoDecoder()
        {
            // 设置FFmpeg库路径
            ffmpeg.RootPath = ScrcpyConfig.FFmpegPath;
        }

        /// <summary>
        /// 初始化解码器
        /// </summary>
        public bool Initialize()
        {
            try
            {
                // 查找H.264解码器
                _codec = ffmpeg.avcodec_find_decoder(AVCodecID.AV_CODEC_ID_H264);
                if (_codec == null)
                {
                    throw new Exception("H.264 decoder not found");
                }

                // 分配解码器上下文
                _codecContext = ffmpeg.avcodec_alloc_context3(_codec);
                if (_codecContext == null)
                {
                    throw new Exception("Could not allocate codec context");
                }

                // 打开解码器
                var result = ffmpeg.avcodec_open2(_codecContext, _codec, null);
                if (result < 0)
                {
                    throw new Exception($"Could not open codec: {result}");
                }

                // 分配帧
                _frame = ffmpeg.av_frame_alloc();
                _frameRgb = ffmpeg.av_frame_alloc();

                if (_frame == null || _frameRgb == null)
                {
                    throw new Exception("Could not allocate frames");
                }

                _isInitialized = true;
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to initialize video decoder: {ex.Message}");
                Dispose();
                return false;
            }
        }

        /// <summary>
        /// 解码视频数据
        /// </summary>
        /// <param name="data">H.264编码的视频数据</param>
        /// <param name="length">数据长度</param>
        /// <returns>是否成功解码出新帧</returns>
        public bool DecodeFrame(byte[] data, int length)
        {
            if (!_isInitialized || data == null || length <= 0)
                return false;

            try
            {
                // 创建数据包
                var packet = ffmpeg.av_packet_alloc();
                if (packet == null)
                    return false;

                try
                {
                    // 设置数据包数据
                    fixed (byte* dataPtr = data)
                    {
                        packet->data = dataPtr;
                        packet->size = length;

                        // 发送数据包到解码器
                        var sendResult = ffmpeg.avcodec_send_packet(_codecContext, packet);
                        if (sendResult < 0)
                        {
                            return false;
                        }

                        // 接收解码后的帧
                        var receiveResult = ffmpeg.avcodec_receive_frame(_codecContext, _frame);
                        if (receiveResult == 0)
                        {
                            // 成功解码出帧
                            return ProcessDecodedFrame();
                        }
                    }
                }
                finally
                {
                    ffmpeg.av_packet_free(&packet);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error decoding frame: {ex.Message}");
            }

            return false;
        }

        /// <summary>
        /// 处理解码后的帧
        /// </summary>
        private bool ProcessDecodedFrame()
        {
            try
            {
                var width = _frame->width;
                var height = _frame->height;

                // 如果尺寸发生变化，重新初始化转换器
                if (Width != width || Height != height)
                {
                    Width = width;
                    Height = height;
                    InitializeSwsContext();
                }

                if (_swsContext == null)
                    return false;

                // 转换为RGB格式
                var rgbLinesize = new int[4];
                var rgbData = new byte_ptrArray4();

                rgbLinesize[0] = Width * 3; // RGB24格式，每像素3字节
                rgbData[0] = _buffer;

                fixed (int* rgbLinesizePtr = rgbLinesize)
                {
                    ffmpeg.sws_scale(_swsContext,
                        _frame->data, _frame->linesize, 0, Height,
                        rgbData, rgbLinesizePtr);
                }

                // 复制RGB数据
                var rgbBytes = new byte[Width * Height * 3];
                Marshal.Copy((IntPtr)_buffer, rgbBytes, 0, rgbBytes.Length);

                // 创建帧数据对象
                var frameData = new FrameData(Width, Height, rgbBytes)
                {
                    FrameNumber = Interlocked.Increment(ref _frameNumber)
                };

                // 更新最新帧
                lock (_lockObject)
                {
                    _latestFrame = frameData;
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error processing decoded frame: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 初始化图像转换上下文
        /// </summary>
        private void InitializeSwsContext()
        {
            // 释放旧的转换器
            if (_swsContext != null)
            {
                ffmpeg.sws_freeContext(_swsContext);
            }

            // 释放旧的缓冲区
            if (_buffer != null)
            {
                ffmpeg.av_free(_buffer);
            }

            // 创建新的转换器
            _swsContext = ffmpeg.sws_getContext(
                Width, Height, _codecContext->pix_fmt,
                Width, Height, AVPixelFormat.AV_PIX_FMT_RGB24,
                ffmpeg.SWS_BILINEAR, null, null, null);

            // 分配RGB缓冲区
            _bufferSize = Width * Height * 3;
            _buffer = (byte*)ffmpeg.av_malloc((ulong)_bufferSize);
        }

        /// <summary>
        /// 获取最新帧的副本
        /// </summary>
        /// <returns>最新帧的副本，如果没有帧则返回null</returns>
        public FrameData GetLatestFrameCopy()
        {
            lock (_lockObject)
            {
                if (_latestFrame == null)
                    return null;

                // 创建数据副本
                var rgbDataCopy = new byte[_latestFrame.RgbData.Length];
                Array.Copy(_latestFrame.RgbData, rgbDataCopy, _latestFrame.RgbData.Length);

                return new FrameData(_latestFrame.Width, _latestFrame.Height, rgbDataCopy)
                {
                    FrameNumber = _latestFrame.FrameNumber,
                    Timestamp = _latestFrame.Timestamp
                };
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_swsContext != null)
            {
                ffmpeg.sws_freeContext(_swsContext);
                _swsContext = null;
            }

            if (_buffer != null)
            {
                ffmpeg.av_free(_buffer);
                _buffer = null;
            }

            if (_frameRgb != null)
            {
                ffmpeg.av_frame_free(&_frameRgb);
                _frameRgb = null;
            }

            if (_frame != null)
            {
                ffmpeg.av_frame_free(&_frame);
                _frame = null;
            }

            if (_codecContext != null)
            {
                ffmpeg.avcodec_free_context(&_codecContext);
                _codecContext = null;
            }

            _isInitialized = false;
        }
    }
}
