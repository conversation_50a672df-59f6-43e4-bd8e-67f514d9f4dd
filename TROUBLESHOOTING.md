# AutoGame 故障排除指南

## 快速解决方案

如果您遇到连接问题，请按以下顺序尝试：

### 1. 自动诊断和修复
```bash
# 运行自动诊断
AutoGame.exe --diagnostic

# 自动修复常见问题
AutoGame.exe --autofix

# 启动修复向导
AutoGame.exe --fix
```

### 2. 手动检查步骤

#### 检查ADB连接
```bash
adb devices
```
应该显示您的设备状态为 `device`

#### 检查USB调试
1. 在Android设备上进入 设置 > 开发者选项
2. 确保"USB调试"已启用
3. 如果看到授权提示，点击"确定"

#### 检查scrcpy-server.jar
```bash
adb shell ls -l /data/local/tmp/scrcpy-server.jar
```
如果文件不存在，运行：
```bash
adb push scrcpy-server.jar /data/local/tmp/
```

## 常见错误及解决方案

### 错误1: "Failed to setup port forwarding"

**原因**: 端口转发设置失败

**解决方案**:
1. 清理现有端口转发：
   ```bash
   adb forward --remove-all
   ```

2. 重启ADB服务器：
   ```bash
   adb kill-server
   adb start-server
   ```

3. 检查端口占用：
   ```bash
   netstat -an | findstr :8080
   netstat -an | findstr :8081
   ```

4. 如果端口被占用，终止占用进程或重启计算机

### 错误2: "Failed to start scrcpy server"

**原因**: scrcpy服务器启动失败

**解决方案**:
1. 检查服务器文件是否存在：
   ```bash
   adb shell ls -l /data/local/tmp/scrcpy-server.jar
   ```

2. 重新推送服务器文件：
   ```bash
   adb push scrcpy-server.jar /data/local/tmp/
   adb shell chmod 755 /data/local/tmp/scrcpy-server.jar
   ```

3. 停止可能运行的服务器进程：
   ```bash
   adb shell pkill -f scrcpy
   adb shell pkill -f app_process
   ```

4. 检查设备权限：
   ```bash
   adb shell getprop ro.debuggable
   ```
   应该返回 `1`

### 错误3: "Failed to connect video stream"

**原因**: 视频流连接失败

**解决方案**:
1. 确保端口转发正常：
   ```bash
   adb forward --list
   ```

2. 检查防火墙设置，确保允许端口8080和8081

3. 尝试重新启动服务器：
   ```bash
   adb shell pkill -f scrcpy
   ```
   然后重新运行程序

### 错误4: "FFmpeg库加载失败"

**原因**: FFmpeg库文件缺失或路径错误

**解决方案**:
1. 检查FFmpeg目录是否存在：
   ```
   ffmpeg/
   ├── avcodec-60.dll
   ├── avformat-60.dll
   ├── avutil-58.dll
   ├── swresample-4.dll
   └── swscale-7.dll
   ```

2. 从 [FFmpeg Builds](https://github.com/BtbN/FFmpeg-Builds/releases) 下载正确版本

3. 确保DLL文件版本匹配

### 错误5: "设备未检测到"

**原因**: 设备连接或驱动问题

**解决方案**:
1. 检查USB连接：
   - 尝试不同的USB端口
   - 更换USB线缆
   - 确保使用数据线而非充电线

2. 检查设备驱动：
   - 在设备管理器中查看是否有未知设备
   - 安装或更新Android驱动程序

3. 检查设备设置：
   - 确保USB调试已启用
   - 尝试切换USB连接模式（文件传输/MTP）

## 设备兼容性问题

### Android版本兼容性
- **支持**: Android 5.0 (API 21) 及以上
- **推荐**: Android 7.0 (API 24) 及以上
- **最佳**: Android 10.0 (API 29) 及以上

### 已知兼容设备
- Samsung Galaxy 系列
- Google Pixel 系列
- OnePlus 系列
- Xiaomi 系列
- Huawei 系列（需要额外设置）

### 已知问题设备
- 某些华为设备需要在开发者选项中启用"仅充电模式下允许ADB调试"
- 某些小米设备需要在开发者选项中启用"USB调试（安全设置）"
- 部分定制ROM可能需要额外权限

## 性能优化

### 降低延迟
1. 使用USB 3.0端口
2. 降低视频分辨率：
   ```csharp
   await client.ConnectAsync(null, 1280, 4000000); // 1280p, 4Mbps
   ```

3. 关闭不必要的后台应用

### 提高稳定性
1. 确保设备电量充足
2. 使用质量好的USB线缆
3. 避免在充电时使用

## 高级故障排除

### 查看详细日志
1. 启用调试日志：
   ```ini
   # 在scrcpy.config中设置
   EnableDebugLog=true
   ```

2. 查看ADB日志：
   ```bash
   adb logcat | findstr scrcpy
   ```

### 手动测试连接
1. 测试ADB连接：
   ```bash
   adb shell echo "Hello"
   ```

2. 测试屏幕捕获：
   ```bash
   adb shell screencap -p > test.png
   ```

3. 测试文件推送：
   ```bash
   adb push test.txt /data/local/tmp/
   ```

### 网络诊断
1. 检查本地端口：
   ```bash
   netstat -an | findstr LISTEN
   ```

2. 测试端口连接：
   ```bash
   telnet 127.0.0.1 8080
   ```

## 重置和清理

### 完全重置
1. 停止所有进程：
   ```bash
   adb kill-server
   taskkill /f /im adb.exe
   ```

2. 清理端口转发：
   ```bash
   adb start-server
   adb forward --remove-all
   ```

3. 重新推送服务器：
   ```bash
   adb push scrcpy-server.jar /data/local/tmp/
   ```

### 清理临时文件
1. 删除设备上的临时文件：
   ```bash
   adb shell rm /data/local/tmp/scrcpy-server.jar
   ```

2. 重新推送：
   ```bash
   adb push scrcpy-server.jar /data/local/tmp/
   ```

## 获取帮助

如果以上方法都无法解决问题：

1. 运行完整诊断：
   ```bash
   AutoGame.exe --diagnostic
   ```

2. 生成诊断报告并查看详细信息

3. 检查GitHub Issues中是否有类似问题

4. 提交新的Issue，包含：
   - 设备型号和Android版本
   - 错误信息
   - 诊断报告
   - 重现步骤

## 预防措施

1. **定期更新**: 保持ADB和驱动程序最新
2. **备份配置**: 保存工作的配置文件
3. **测试环境**: 在新设备上先进行基本测试
4. **监控性能**: 注意CPU和内存使用情况

---

**记住**: 大多数连接问题都可以通过重启ADB服务器和重新推送服务器文件来解决。
