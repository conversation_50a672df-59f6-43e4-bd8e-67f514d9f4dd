using System;
using System.Diagnostics;
using System.Threading.Tasks;

namespace AutoGame
{
    /// <summary>
    /// ADB命令测试工具
    /// </summary>
    public static class TestAdbCommands
    {
        /// <summary>
        /// 测试所有ADB命令
        /// </summary>
        public static async Task RunAllTestsAsync()
        {
            Console.WriteLine("=== ADB命令测试工具 ===");
            Console.WriteLine();

            await TestAdbVersionAsync();
            await TestDeviceListAsync();
            await TestDevicePropertiesAsync();
            await TestPortForwardingAsync();
            await TestFileOperationsAsync();
            
            Console.WriteLine();
            Console.WriteLine("=== 测试完成 ===");
        }

        /// <summary>
        /// 测试ADB版本
        /// </summary>
        public static async Task TestAdbVersionAsync()
        {
            Console.WriteLine("1. 测试ADB版本...");
            var result = await RunCommandAsync("adb", "version");
            Console.WriteLine($"   结果: {result}");
            Console.WriteLine();
        }

        /// <summary>
        /// 测试设备列表
        /// </summary>
        public static async Task TestDeviceListAsync()
        {
            Console.WriteLine("2. 测试设备列表...");
            var result = await RunCommandAsync("adb", "devices -l");
            Console.WriteLine($"   结果: {result}");
            Console.WriteLine();
        }

        /// <summary>
        /// 测试设备属性
        /// </summary>
        public static async Task TestDevicePropertiesAsync()
        {
            Console.WriteLine("3. 测试设备属性...");
            
            // 获取第一个设备ID
            var devicesResult = await RunCommandAsync("adb", "devices");
            string deviceId = null;
            
            var lines = devicesResult.Split('\n');
            foreach (var line in lines)
            {
                if (line.Contains("\tdevice"))
                {
                    deviceId = line.Split('\t')[0];
                    break;
                }
            }

            if (string.IsNullOrEmpty(deviceId))
            {
                Console.WriteLine("   ❌ 未找到设备");
                return;
            }

            Console.WriteLine($"   使用设备: {deviceId}");

            // 测试各种属性
            var tests = new[]
            {
                ("设备型号", $"-s {deviceId} shell getprop ro.product.model"),
                ("Android版本", $"-s {deviceId} shell getprop ro.build.version.release"),
                ("API级别", $"-s {deviceId} shell getprop ro.build.version.sdk"),
                ("屏幕尺寸", $"-s {deviceId} shell wm size"),
                ("屏幕密度", $"-s {deviceId} shell wm density"),
                ("USB调试状态", $"-s {deviceId} shell getprop ro.debuggable"),
                ("构建类型", $"-s {deviceId} shell getprop ro.build.type")
            };

            foreach (var (name, command) in tests)
            {
                var result = await RunCommandAsync("adb", command);
                Console.WriteLine($"   {name}: {result.Trim()}");
            }
            Console.WriteLine();
        }

        /// <summary>
        /// 测试端口转发
        /// </summary>
        public static async Task TestPortForwardingAsync()
        {
            Console.WriteLine("4. 测试端口转发...");
            
            // 获取设备ID
            var devicesResult = await RunCommandAsync("adb", "devices");
            string deviceId = null;
            
            var lines = devicesResult.Split('\n');
            foreach (var line in lines)
            {
                if (line.Contains("\tdevice"))
                {
                    deviceId = line.Split('\t')[0];
                    break;
                }
            }

            if (string.IsNullOrEmpty(deviceId))
            {
                Console.WriteLine("   ❌ 未找到设备");
                return;
            }

            // 清理现有转发
            Console.WriteLine("   清理现有端口转发...");
            var removeResult = await RunCommandAsync("adb", $"-s {deviceId} forward --remove-all");
            Console.WriteLine($"   清理结果: {(string.IsNullOrEmpty(removeResult.Trim()) ? "成功" : removeResult)}");

            // 列出当前转发
            Console.WriteLine("   当前端口转发列表:");
            var listResult = await RunCommandAsync("adb", $"-s {deviceId} forward --list");
            if (string.IsNullOrEmpty(listResult.Trim()))
            {
                Console.WriteLine("   (无端口转发)");
            }
            else
            {
                Console.WriteLine($"   {listResult}");
            }

            // 测试设置端口转发
            Console.WriteLine("   测试设置端口转发...");
            var forwardResult = await RunCommandAsync("adb", $"-s {deviceId} forward tcp:8080 tcp:8080");
            Console.WriteLine($"   设置结果: {(string.IsNullOrEmpty(forwardResult.Trim()) ? "成功" : forwardResult)}");

            // 再次列出转发
            Console.WriteLine("   设置后的端口转发列表:");
            listResult = await RunCommandAsync("adb", $"-s {deviceId} forward --list");
            if (string.IsNullOrEmpty(listResult.Trim()))
            {
                Console.WriteLine("   (无端口转发)");
            }
            else
            {
                Console.WriteLine($"   {listResult}");
            }

            // 清理测试转发
            await RunCommandAsync("adb", $"-s {deviceId} forward --remove tcp:8080");
            Console.WriteLine("   已清理测试端口转发");
            Console.WriteLine();
        }

        /// <summary>
        /// 测试文件操作
        /// </summary>
        public static async Task TestFileOperationsAsync()
        {
            Console.WriteLine("5. 测试文件操作...");
            
            // 获取设备ID
            var devicesResult = await RunCommandAsync("adb", "devices");
            string deviceId = null;
            
            var lines = devicesResult.Split('\n');
            foreach (var line in lines)
            {
                if (line.Contains("\tdevice"))
                {
                    deviceId = line.Split('\t')[0];
                    break;
                }
            }

            if (string.IsNullOrEmpty(deviceId))
            {
                Console.WriteLine("   ❌ 未找到设备");
                return;
            }

            // 测试目录访问
            Console.WriteLine("   测试目录访问...");
            var lsResult = await RunCommandAsync("adb", $"-s {deviceId} shell ls /data/local/tmp/");
            Console.WriteLine($"   /data/local/tmp/ 内容: {lsResult}");

            // 检查scrcpy-server.jar
            Console.WriteLine("   检查scrcpy-server.jar...");
            var serverResult = await RunCommandAsync("adb", $"-s {deviceId} shell ls -l /data/local/tmp/scrcpy-server.jar");
            if (serverResult.Contains("No such file") || serverResult.Contains("not found"))
            {
                Console.WriteLine("   ❌ scrcpy-server.jar 不存在");
                
                // 检查本地文件
                if (System.IO.File.Exists("scrcpy-server.jar"))
                {
                    Console.WriteLine("   本地scrcpy-server.jar存在，尝试推送...");
                    var pushResult = await RunCommandAsync("adb", $"-s {deviceId} push scrcpy-server.jar /data/local/tmp/");
                    Console.WriteLine($"   推送结果: {pushResult}");
                }
                else
                {
                    Console.WriteLine("   ❌ 本地scrcpy-server.jar也不存在");
                }
            }
            else
            {
                Console.WriteLine($"   ✅ scrcpy-server.jar存在: {serverResult}");
            }

            // 测试权限
            Console.WriteLine("   测试屏幕捕获权限...");
            var screencapResult = await RunCommandAsync("adb", $"-s {deviceId} shell screencap -p > nul");
            if (screencapResult.Contains("Permission denied"))
            {
                Console.WriteLine("   ❌ 屏幕捕获权限不足");
            }
            else
            {
                Console.WriteLine("   ✅ 屏幕捕获权限正常");
            }

            Console.WriteLine();
        }

        /// <summary>
        /// 运行命令并返回输出
        /// </summary>
        private static async Task<string> RunCommandAsync(string fileName, string arguments)
        {
            try
            {
                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = fileName,
                        Arguments = arguments,
                        UseShellExecute = false,
                        CreateNoWindow = true,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true
                    }
                };

                process.Start();
                var output = await process.StandardOutput.ReadToEndAsync();
                var error = await process.StandardError.ReadToEndAsync();
                process.WaitForExit();

                if (!string.IsNullOrEmpty(error))
                {
                    return $"ERROR: {error}";
                }

                return output;
            }
            catch (Exception ex)
            {
                return $"EXCEPTION: {ex.Message}";
            }
        }

        /// <summary>
        /// 交互式测试
        /// </summary>
        public static async Task RunInteractiveTestAsync()
        {
            Console.WriteLine("=== 交互式ADB测试 ===");
            Console.WriteLine("输入ADB命令进行测试 (不需要输入'adb'前缀)");
            Console.WriteLine("输入 'quit' 退出");
            Console.WriteLine();

            while (true)
            {
                Console.Write("adb> ");
                var input = Console.ReadLine();

                if (string.IsNullOrEmpty(input) || input.ToLower() == "quit")
                {
                    break;
                }

                var result = await RunCommandAsync("adb", input);
                Console.WriteLine($"结果: {result}");
                Console.WriteLine();
            }
        }
    }
}
