using System;
using System.Threading.Tasks;
using System.Threading;
using System.Drawing;

namespace AutoGame
{
    /// <summary>
    /// 游戏自动化示例类
    /// 演示如何使用ScrcpyClient进行游戏自动化操作
    /// </summary>
    public class GameAutomationExample
    {
        private ScrcpyClient _client;
        private bool _isRunning;

        public GameAutomationExample()
        {
            _client = new ScrcpyClient();
        }

        /// <summary>
        /// 初始化并连接设备
        /// </summary>
        public async Task<bool> InitializeAsync()
        {
            Console.WriteLine("正在连接Android设备...");
            bool connected = await _client.ConnectAsync();
            
            if (connected)
            {
                Console.WriteLine($"设备连接成功: {_client.DeviceInfo}");
                return true;
            }
            else
            {
                Console.WriteLine("设备连接失败");
                return false;
            }
        }

        /// <summary>
        /// 自动点击示例 - 每隔一定时间点击屏幕中心
        /// </summary>
        public async Task AutoClickExample()
        {
            if (!_client.IsConnected)
            {
                Console.WriteLine("设备未连接");
                return;
            }

            var deviceInfo = _client.DeviceInfo;
            int centerX = deviceInfo.ScreenWidth / 2;
            int centerY = deviceInfo.ScreenHeight / 2;

            Console.WriteLine($"开始自动点击，目标位置: ({centerX}, {centerY})");
            Console.WriteLine("按任意键停止...");

            _isRunning = true;
            var clickTask = Task.Run(async () =>
            {
                while (_isRunning)
                {
                    await _client.DeviceController.ClickAsync(centerX, centerY);
                    Console.WriteLine($"点击了屏幕中心 ({centerX}, {centerY})");
                    await Task.Delay(2000); // 每2秒点击一次
                }
            });

            Console.ReadKey();
            _isRunning = false;
            await clickTask;
            Console.WriteLine("自动点击已停止");
        }

        /// <summary>
        /// 屏幕截图示例
        /// </summary>
        public async Task ScreenshotExample()
        {
            if (!_client.IsConnected)
            {
                Console.WriteLine("设备未连接");
                return;
            }

            Console.WriteLine("正在获取屏幕截图...");
            
            // 等待一下确保有视频帧
            await Task.Delay(1000);
            
            var frame = _client.GetLatestFrame();
            if (frame != null)
            {
                var bitmap = frame.ToBitmap();
                string filename = $"screenshot_{DateTime.Now:yyyyMMdd_HHmmss}.png";
                bitmap.Save(filename);
                Console.WriteLine($"截图已保存: {filename}");
                Console.WriteLine($"图像尺寸: {frame.Width}x{frame.Height}");
                Console.WriteLine($"帧序号: {frame.FrameNumber}");
                bitmap.Dispose();
            }
            else
            {
                Console.WriteLine("无法获取屏幕帧");
            }
        }

        /// <summary>
        /// 滑动操作示例 - 模拟滚动
        /// </summary>
        public async Task SwipeExample()
        {
            if (!_client.IsConnected)
            {
                Console.WriteLine("设备未连接");
                return;
            }

            var deviceInfo = _client.DeviceInfo;
            int centerX = deviceInfo.ScreenWidth / 2;
            int startY = deviceInfo.ScreenHeight * 3 / 4;
            int endY = deviceInfo.ScreenHeight / 4;

            Console.WriteLine("执行向上滑动操作...");
            bool success = await _client.DeviceController.SwipeAsync(centerX, startY, centerX, endY, 800);
            Console.WriteLine(success ? "滑动操作成功" : "滑动操作失败");
        }

        /// <summary>
        /// 游戏操作序列示例
        /// </summary>
        public async Task GameSequenceExample()
        {
            if (!_client.IsConnected)
            {
                Console.WriteLine("设备未连接");
                return;
            }

            Console.WriteLine("开始执行游戏操作序列...");

            // 1. 截图查看当前状态
            await ScreenshotExample();

            // 2. 点击游戏开始按钮（假设在屏幕中心）
            var deviceInfo = _client.DeviceInfo;
            int centerX = deviceInfo.ScreenWidth / 2;
            int centerY = deviceInfo.ScreenHeight / 2;
            
            Console.WriteLine("点击游戏开始按钮...");
            await _client.DeviceController.ClickAsync(centerX, centerY);
            await Task.Delay(2000);

            // 3. 执行一系列游戏操作
            Console.WriteLine("执行游戏操作...");
            
            // 向右滑动
            await _client.DeviceController.SwipeAsync(
                centerX - 100, centerY, 
                centerX + 100, centerY, 500);
            await Task.Delay(1000);

            // 向上滑动
            await _client.DeviceController.SwipeAsync(
                centerX, centerY + 100, 
                centerX, centerY - 100, 500);
            await Task.Delay(1000);

            // 长按操作
            Console.WriteLine("执行长按操作...");
            await _client.DeviceController.LongPressAsync(centerX, centerY, 1500);
            await Task.Delay(1000);

            // 4. 按返回键
            Console.WriteLine("按下返回键...");
            await _client.DeviceController.PressBackAsync();

            Console.WriteLine("游戏操作序列完成");
        }

        /// <summary>
        /// 实时帧监控示例
        /// </summary>
        public async Task FrameMonitorExample()
        {
            if (!_client.IsConnected)
            {
                Console.WriteLine("设备未连接");
                return;
            }

            Console.WriteLine("开始实时帧监控...");
            Console.WriteLine("按任意键停止...");

            _isRunning = true;
            var monitorTask = Task.Run(async () =>
            {
                long lastFrameNumber = -1;
                while (_isRunning)
                {
                    var frame = _client.GetLatestFrame();
                    if (frame != null && frame.FrameNumber != lastFrameNumber)
                    {
                        Console.WriteLine($"新帧: #{frame.FrameNumber}, 尺寸: {frame.Width}x{frame.Height}, 时间: {frame.Timestamp:HH:mm:ss.fff}");
                        lastFrameNumber = frame.FrameNumber;
                    }
                    await Task.Delay(100); // 每100ms检查一次
                }
            });

            Console.ReadKey();
            _isRunning = false;
            await monitorTask;
            Console.WriteLine("帧监控已停止");
        }

        /// <summary>
        /// 设备控制示例
        /// </summary>
        public async Task DeviceControlExample()
        {
            if (!_client.IsConnected)
            {
                Console.WriteLine("设备未连接");
                return;
            }

            Console.WriteLine("设备控制示例:");

            // 息屏
            Console.WriteLine("息屏...");
            await _client.DeviceController.SetScreenPowerAsync(false);
            await Task.Delay(2000);

            // 亮屏
            Console.WriteLine("亮屏...");
            await _client.DeviceController.SetScreenPowerAsync(true);
            await Task.Delay(1000);

            // 按Home键
            Console.WriteLine("按下Home键...");
            await _client.DeviceController.PressHomeAsync();
            await Task.Delay(1000);

            // 打开最近任务
            Console.WriteLine("打开最近任务...");
            await _client.DeviceController.PressAppSwitchAsync();
            await Task.Delay(2000);

            // 返回
            Console.WriteLine("按下返回键...");
            await _client.DeviceController.PressBackAsync();

            Console.WriteLine("设备控制示例完成");
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        public async Task CleanupAsync()
        {
            _isRunning = false;
            if (_client != null)
            {
                await _client.DisconnectAsync();
                _client.Dispose();
            }
        }

        /// <summary>
        /// 运行所有示例
        /// </summary>
        public async Task RunAllExamplesAsync()
        {
            try
            {
                if (!await InitializeAsync())
                {
                    return;
                }

                Console.WriteLine("\n=== 屏幕截图示例 ===");
                await ScreenshotExample();

                Console.WriteLine("\n=== 滑动操作示例 ===");
                await SwipeExample();

                Console.WriteLine("\n=== 设备控制示例 ===");
                await DeviceControlExample();

                Console.WriteLine("\n=== 游戏操作序列示例 ===");
                await GameSequenceExample();

                Console.WriteLine("\n所有示例执行完成!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"示例执行出错: {ex.Message}");
            }
            finally
            {
                await CleanupAsync();
            }
        }
    }
}
