using System;
using System.Diagnostics;
using System.IO;
using System.Net.Sockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace AutoGame
{
    /// <summary>
    /// Scrcpy客户端类，负责连接和管理设备
    /// </summary>
    public class ScrcpyClient : IDisposable
    {
        private Process _adbProcess;
        private Process _serverProcess;
        private TcpClient _videoClient;
        private NetworkStream _videoStream;
        private VideoDecoder _videoDecoder;
        private DeviceController _deviceController;
        private DeviceInfo _deviceInfo;
        private CancellationTokenSource _cancellationTokenSource;
        private Task _videoReceiveTask;
        private bool _isConnected;

        /// <summary>
        /// 是否已连接
        /// </summary>
        public bool IsConnected => _isConnected;

        /// <summary>
        /// 设备信息
        /// </summary>
        public DeviceInfo DeviceInfo => _deviceInfo;

        /// <summary>
        /// 视频解码器
        /// </summary>
        public VideoDecoder VideoDecoder => _videoDecoder;

        /// <summary>
        /// 设备控制器
        /// </summary>
        public DeviceController DeviceController => _deviceController;

        /// <summary>
        /// 获取最新的一帧图像
        /// </summary>
        /// <returns>最新帧数据，如果没有则返回null</returns>
        public FrameData GetLatestFrame()
        {
            return _videoDecoder?.GetLatestFrameCopy();
        }

        /// <summary>
        /// 连接到安卓设备
        /// </summary>
        /// <param name="deviceId">设备ID，null表示连接第一个可用设备</param>
        /// <param name="maxSize">最大分辨率</param>
        /// <param name="bitRate">比特率</param>
        /// <returns>是否连接成功</returns>
        public async Task<bool> ConnectAsync(string deviceId = null, int maxSize = 1920, int bitRate = 8000000)
        {
            try
            {
                Console.WriteLine("Starting Scrcpy connection...");

                // 1. 获取设备信息
                _deviceInfo = await GetDeviceInfoAsync(deviceId);
                if (_deviceInfo == null)
                {
                    Console.WriteLine("Failed to get device info");
                    return false;
                }

                Console.WriteLine($"Device found: {_deviceInfo}");

                // 2. 设置端口转发
                if (!await SetupPortForwardingAsync(_deviceInfo.DeviceId))
                {
                    Console.WriteLine("Failed to setup port forwarding");
                    return false;
                }

                // 3. 启动scrcpy服务器
                if (!await StartScrcpyServerAsync(_deviceInfo.DeviceId, maxSize, bitRate))
                {
                    Console.WriteLine("Failed to start scrcpy server");
                    return false;
                }

                // 4. 连接视频流
                if (!await ConnectVideoStreamAsync())
                {
                    Console.WriteLine("Failed to connect video stream");
                    return false;
                }

                // 5. 初始化视频解码器
                _videoDecoder = new VideoDecoder();
                if (!_videoDecoder.Initialize())
                {
                    Console.WriteLine("Failed to initialize video decoder");
                    return false;
                }

                // 6. 连接控制流
                _deviceController = new DeviceController();
                if (!await _deviceController.ConnectAsync("127.0.0.1", ScrcpyProtocol.DEFAULT_CONTROL_PORT, _deviceInfo))
                {
                    Console.WriteLine("Failed to connect device controller");
                    return false;
                }

                // 7. 开始接收视频数据
                _cancellationTokenSource = new CancellationTokenSource();
                _videoReceiveTask = Task.Run(() => ReceiveVideoDataAsync(_cancellationTokenSource.Token));

                _isConnected = true;
                Console.WriteLine("Scrcpy connection established successfully!");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to connect: {ex.Message}");
                await DisconnectAsync();
                return false;
            }
        }

        /// <summary>
        /// 断开连接
        /// </summary>
        public async Task DisconnectAsync()
        {
            _isConnected = false;

            try
            {
                // 停止视频接收任务
                _cancellationTokenSource?.Cancel();
                if (_videoReceiveTask != null)
                {
                    await _videoReceiveTask;
                }

                // 释放资源
                _deviceController?.Dispose();
                _videoDecoder?.Dispose();
                _videoStream?.Close();
                _videoClient?.Close();

                // 停止服务器进程
                _serverProcess?.Kill();
                _serverProcess?.Dispose();

                // 移除端口转发
                if (_deviceInfo != null)
                {
                    await RemovePortForwardingAsync(_deviceInfo.DeviceId);
                }

                Console.WriteLine("Scrcpy disconnected");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error during disconnect: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取设备信息
        /// </summary>
        private async Task<DeviceInfo> GetDeviceInfoAsync(string deviceId)
        {
            try
            {
                // 如果没有指定设备ID，获取第一个可用设备
                if (string.IsNullOrEmpty(deviceId))
                {
                    var devicesOutput = await RunAdbCommandAsync("devices");
                    var lines = devicesOutput.Split('\n');
                    foreach (var line in lines)
                    {
                        if (line.Contains("\tdevice"))
                        {
                            deviceId = line.Split('\t')[0];
                            break;
                        }
                    }
                }

                if (string.IsNullOrEmpty(deviceId))
                {
                    return null;
                }

                var deviceInfo = new DeviceInfo { DeviceId = deviceId };

                // 获取设备名称
                var nameOutput = await RunAdbCommandAsync($"-s {deviceId} shell getprop ro.product.model");
                deviceInfo.DeviceName = nameOutput.Trim();

                // 获取屏幕尺寸
                var sizeOutput = await RunAdbCommandAsync($"-s {deviceId} shell wm size");
                var sizeMatch = System.Text.RegularExpressions.Regex.Match(sizeOutput, @"(\d+)x(\d+)");
                if (sizeMatch.Success)
                {
                    deviceInfo.ScreenWidth = int.Parse(sizeMatch.Groups[1].Value);
                    deviceInfo.ScreenHeight = int.Parse(sizeMatch.Groups[2].Value);
                }

                // 获取Android版本
                var versionOutput = await RunAdbCommandAsync($"-s {deviceId} shell getprop ro.build.version.release");
                deviceInfo.AndroidVersion = versionOutput.Trim();

                // 获取API级别
                var apiOutput = await RunAdbCommandAsync($"-s {deviceId} shell getprop ro.build.version.sdk");
                if (int.TryParse(apiOutput.Trim(), out int apiLevel))
                {
                    deviceInfo.ApiLevel = apiLevel;
                }

                return deviceInfo;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to get device info: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 设置端口转发
        /// </summary>
        private async Task<bool> SetupPortForwardingAsync(string deviceId)
        {
            try
            {
                // 转发视频端口
                var videoResult = await RunAdbCommandAsync($"-s {deviceId} forward tcp:{ScrcpyProtocol.DEFAULT_SERVER_PORT} localabstract:scrcpy");
                
                // 转发控制端口
                var controlResult = await RunAdbCommandAsync($"-s {deviceId} forward tcp:{ScrcpyProtocol.DEFAULT_CONTROL_PORT} localabstract:scrcpy_control");

                return !string.IsNullOrEmpty(videoResult) && !string.IsNullOrEmpty(controlResult);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to setup port forwarding: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 移除端口转发
        /// </summary>
        private async Task RemovePortForwardingAsync(string deviceId)
        {
            try
            {
                await RunAdbCommandAsync($"-s {deviceId} forward --remove tcp:{ScrcpyProtocol.DEFAULT_SERVER_PORT}");
                await RunAdbCommandAsync($"-s {deviceId} forward --remove tcp:{ScrcpyProtocol.DEFAULT_CONTROL_PORT}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to remove port forwarding: {ex.Message}");
            }
        }

        /// <summary>
        /// 启动scrcpy服务器
        /// </summary>
        private async Task<bool> StartScrcpyServerAsync(string deviceId, int maxSize, int bitRate)
        {
            try
            {
                // 构建服务器启动命令
                var serverArgs = $"log_level=info " +
                               $"max_size={maxSize} " +
                               $"bit_rate={bitRate} " +
                               $"max_fps={ScrcpyProtocol.DefaultConfig.MAX_FPS} " +
                               $"lock_video_orientation={ScrcpyProtocol.DefaultConfig.LOCK_VIDEO_ORIENTATION} " +
                               $"tunnel_forward={ScrcpyProtocol.DefaultConfig.TUNNEL_FORWARD} " +
                               $"crop= " +
                               $"control={ScrcpyProtocol.DefaultConfig.CONTROL_ENABLED} " +
                               $"display_id={ScrcpyProtocol.DefaultConfig.DISPLAY_ID} " +
                               $"show_touches={ScrcpyProtocol.DefaultConfig.SHOW_TOUCHES} " +
                               $"stay_awake={ScrcpyProtocol.DefaultConfig.STAY_AWAKE} " +
                               $"codec_options= " +
                               $"encoder_name=";

                var command = $"-s {deviceId} shell CLASSPATH=/data/local/tmp/scrcpy-server.jar app_process / com.genymobile.scrcpy.Server {ScrcpyProtocol.PROTOCOL_VERSION} {serverArgs}";

                _serverProcess = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "adb",
                        Arguments = command,
                        UseShellExecute = false,
                        CreateNoWindow = true,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true
                    }
                };

                _serverProcess.Start();

                // 等待服务器启动
                await Task.Delay(2000);

                return _serverProcess != null && !_serverProcess.HasExited;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to start scrcpy server: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 连接视频流
        /// </summary>
        private async Task<bool> ConnectVideoStreamAsync()
        {
            try
            {
                _videoClient = new TcpClient();
                await _videoClient.ConnectAsync("127.0.0.1", ScrcpyProtocol.DEFAULT_SERVER_PORT);
                _videoStream = _videoClient.GetStream();

                Console.WriteLine("Video stream connected");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to connect video stream: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 接收视频数据
        /// </summary>
        private async Task ReceiveVideoDataAsync(CancellationToken cancellationToken)
        {
            var buffer = new byte[65536]; // 64KB缓冲区

            try
            {
                while (!cancellationToken.IsCancellationRequested && _videoStream != null)
                {
                    var bytesRead = await _videoStream.ReadAsync(buffer, 0, buffer.Length, cancellationToken);
                    if (bytesRead > 0)
                    {
                        // 解码视频数据
                        _videoDecoder?.DecodeFrame(buffer, bytesRead);
                    }
                    else
                    {
                        // 连接断开
                        break;
                    }
                }
            }
            catch (OperationCanceledException)
            {
                // 正常取消
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error receiving video data: {ex.Message}");
            }
        }

        /// <summary>
        /// 运行ADB命令
        /// </summary>
        private async Task<string> RunAdbCommandAsync(string arguments)
        {
            try
            {
                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "adb",
                        Arguments = arguments,
                        UseShellExecute = false,
                        CreateNoWindow = true,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true
                    }
                };

                process.Start();
                var output = await process.StandardOutput.ReadToEndAsync();
                await process.WaitForExitAsync();

                return output;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to run adb command: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            DisconnectAsync().Wait();
        }
    }
}
