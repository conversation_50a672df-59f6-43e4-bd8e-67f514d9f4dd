using System;
using System.Diagnostics;
using System.IO;
using System.Net.Sockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace AutoGame
{
    /// <summary>
    /// Scrcpy客户端类，负责连接和管理设备
    /// </summary>
    public class ScrcpyClient : IDisposable
    {
        private Process _serverProcess;
        private TcpClient _videoClient;
        private NetworkStream _videoStream;
        private VideoDecoder _videoDecoder;
        private DeviceController _deviceController;
        private DeviceInfo _deviceInfo;
        private CancellationTokenSource _cancellationTokenSource;
        private Task _videoReceiveTask;
        private bool _isConnected;

        /// <summary>
        /// 是否已连接
        /// </summary>
        public bool IsConnected => _isConnected;

        /// <summary>
        /// 设备信息
        /// </summary>
        public DeviceInfo DeviceInfo => _deviceInfo;

        /// <summary>
        /// 视频解码器
        /// </summary>
        public VideoDecoder VideoDecoder => _videoDecoder;

        /// <summary>
        /// 设备控制器
        /// </summary>
        public DeviceController DeviceController => _deviceController;

        /// <summary>
        /// 获取最新的一帧图像
        /// </summary>
        /// <returns>最新帧数据，如果没有则返回null</returns>
        public FrameData GetLatestFrame()
        {
            return _videoDecoder?.GetLatestFrameCopy();
        }

        /// <summary>
        /// 连接到安卓设备
        /// </summary>
        /// <param name="deviceId">设备ID，null表示连接第一个可用设备</param>
        /// <param name="maxSize">最大分辨率</param>
        /// <param name="bitRate">比特率</param>
        /// <returns>是否连接成功</returns>
        public async Task<bool> ConnectAsync(string deviceId = null, int maxSize = 1920, int bitRate = 8000000)
        {
            try
            {
                Console.WriteLine("========================================");
                Console.WriteLine("开始连接Scrcpy服务器");
                Console.WriteLine("========================================");
                Console.WriteLine($"配置参数: 最大分辨率={maxSize}, 比特率={bitRate}");
                Console.WriteLine();

                // 1. 获取设备信息
                _deviceInfo = await GetDeviceInfoAsync(deviceId);
                if (_deviceInfo == null)
                {
                    Console.WriteLine("❌ 获取设备信息失败");
                    return false;
                }

                Console.WriteLine($"✅ 设备连接成功: {_deviceInfo}");
                Console.WriteLine();

                // 2. 设置初始端口转发
                if (!await SetupInitialPortForwardingAsync(_deviceInfo.DeviceId))
                {
                    Console.WriteLine("❌ 初始端口转发设置失败");
                    return false;
                }

                Console.WriteLine("✅ 初始端口转发设置成功");
                Console.WriteLine();

                // 3. 启动scrcpy服务器
                if (!await StartScrcpyServerAsync(_deviceInfo.DeviceId, maxSize, bitRate))
                {
                    Console.WriteLine("❌ 启动scrcpy服务器失败");
                    return false;
                }

                Console.WriteLine("✅ Scrcpy服务器启动成功");
                Console.WriteLine();

                // 4. 验证端口转发
                if (!await SetupPortForwardingAsync(_deviceInfo.DeviceId))
                {
                    Console.WriteLine("❌ 端口转发验证失败");
                    return false;
                }

                Console.WriteLine("✅ 端口转发验证成功");
                Console.WriteLine();

                // 5. 连接视频流
                if (!await ConnectVideoStreamAsync())
                {
                    Console.WriteLine("❌ 视频流连接失败");
                    return false;
                }

                Console.WriteLine("✅ 视频流连接成功");
                Console.WriteLine();

                // 6. 初始化视频解码器
                Console.WriteLine("=== 初始化视频解码器 ===");
                _videoDecoder = new VideoDecoder();
                if (!_videoDecoder.Initialize())
                {
                    Console.WriteLine("❌ 视频解码器初始化失败");
                    return false;
                }
                Console.WriteLine("✅ 视频解码器初始化成功");
                Console.WriteLine();

                // 7. 连接控制流
                Console.WriteLine("=== 连接设备控制器 ===");
                _deviceController = new DeviceController();
                if (!await _deviceController.ConnectAsync("127.0.0.1", ScrcpyProtocol.DEFAULT_CONTROL_PORT, _deviceInfo))
                {
                    Console.WriteLine("❌ 设备控制器连接失败");
                    return false;
                }
                Console.WriteLine("✅ 设备控制器连接成功");
                Console.WriteLine();

                // 8. 开始接收视频数据
                Console.WriteLine("=== 启动视频数据接收 ===");
                _cancellationTokenSource = new CancellationTokenSource();
                _videoReceiveTask = Task.Run(() => ReceiveVideoDataAsync(_cancellationTokenSource.Token));
                Console.WriteLine("✅ 视频数据接收已启动");

                _isConnected = true;
                Console.WriteLine();
                Console.WriteLine("========================================");
                Console.WriteLine("🎉 Scrcpy连接建立成功！");
                Console.WriteLine("========================================");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 连接过程中发生异常: {ex.Message}");
                Console.WriteLine($"   异常类型: {ex.GetType().Name}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"   内部异常: {ex.InnerException.Message}");
                }
                Console.WriteLine();
                Console.WriteLine("正在清理资源...");
                await DisconnectAsync();
                return false;
            }
        }

        /// <summary>
        /// 断开连接
        /// </summary>
        public async Task DisconnectAsync()
        {
            _isConnected = false;

            try
            {
                // 停止视频接收任务
                _cancellationTokenSource?.Cancel();
                if (_videoReceiveTask != null)
                {
                    await _videoReceiveTask;
                }

                // 释放资源
                _deviceController?.Dispose();
                _videoDecoder?.Dispose();
                _videoStream?.Close();
                _videoClient?.Close();

                // 停止服务器进程
                _serverProcess?.Kill();
                _serverProcess?.Dispose();

                // 移除端口转发
                if (_deviceInfo != null)
                {
                    await RemovePortForwardingAsync(_deviceInfo.DeviceId);
                }

                Console.WriteLine("Scrcpy disconnected");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error during disconnect: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取设备信息
        /// </summary>
        private async Task<DeviceInfo> GetDeviceInfoAsync(string deviceId)
        {
            try
            {
                Console.WriteLine("=== 获取设备信息 ===");

                // 如果没有指定设备ID，获取第一个可用设备
                if (string.IsNullOrEmpty(deviceId))
                {
                    Console.WriteLine("1. 扫描可用设备...");
                    var devicesOutput = await RunAdbCommandAsync("devices");
                    Console.WriteLine($"   设备列表输出: {devicesOutput}");

                    var lines = devicesOutput.Split('\n');
                    foreach (var line in lines)
                    {
                        if (line.Contains("\tdevice"))
                        {
                            deviceId = line.Split('\t')[0];
                            Console.WriteLine($"   找到设备: {deviceId}");
                            break;
                        }
                    }
                }

                if (string.IsNullOrEmpty(deviceId))
                {
                    Console.WriteLine("   ❌ 未找到可用设备");
                    return null;
                }

                Console.WriteLine($"2. 获取设备详细信息 (ID: {deviceId})...");
                var deviceInfo = new DeviceInfo { DeviceId = deviceId };

                // 获取设备名称
                Console.WriteLine("   获取设备名称...");
                var nameOutput = await RunAdbCommandAsync($"-s {deviceId} shell getprop ro.product.model");
                deviceInfo.DeviceName = nameOutput.Trim();
                Console.WriteLine($"   设备名称: {deviceInfo.DeviceName}");

                // 获取屏幕尺寸
                Console.WriteLine("   获取屏幕尺寸...");
                var sizeOutput = await RunAdbCommandAsync($"-s {deviceId} shell wm size");
                Console.WriteLine($"   屏幕尺寸输出: {sizeOutput}");
                var sizeMatch = System.Text.RegularExpressions.Regex.Match(sizeOutput, @"(\d+)x(\d+)");
                if (sizeMatch.Success)
                {
                    deviceInfo.ScreenWidth = int.Parse(sizeMatch.Groups[1].Value);
                    deviceInfo.ScreenHeight = int.Parse(sizeMatch.Groups[2].Value);
                    Console.WriteLine($"   屏幕尺寸: {deviceInfo.ScreenWidth}x{deviceInfo.ScreenHeight}");
                }
                else
                {
                    Console.WriteLine("   ⚠️ 无法解析屏幕尺寸");
                }

                // 获取Android版本
                Console.WriteLine("   获取Android版本...");
                var versionOutput = await RunAdbCommandAsync($"-s {deviceId} shell getprop ro.build.version.release");
                deviceInfo.AndroidVersion = versionOutput.Trim();
                Console.WriteLine($"   Android版本: {deviceInfo.AndroidVersion}");

                // 获取API级别
                Console.WriteLine("   获取API级别...");
                var apiOutput = await RunAdbCommandAsync($"-s {deviceId} shell getprop ro.build.version.sdk");
                if (int.TryParse(apiOutput.Trim(), out int apiLevel))
                {
                    deviceInfo.ApiLevel = apiLevel;
                    Console.WriteLine($"   API级别: {deviceInfo.ApiLevel}");
                }
                else
                {
                    Console.WriteLine($"   ⚠️ 无法解析API级别: {apiOutput}");
                }

                // 检查USB调试状态
                Console.WriteLine("   检查USB调试状态...");
                var debuggableOutput = await RunAdbCommandAsync($"-s {deviceId} shell getprop ro.debuggable");
                Console.WriteLine($"   USB调试状态: {(debuggableOutput.Trim() == "1" ? "✅ 已启用" : "⚠️ 未启用或受限")}");

                deviceInfo.IsConnected = true;
                Console.WriteLine("   ✅ 设备信息获取完成");

                return deviceInfo;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 获取设备信息异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 设置初始端口转发 (用于服务器启动)
        /// </summary>
        private async Task<bool> SetupInitialPortForwardingAsync(string deviceId)
        {
            try
            {
                Console.WriteLine("=== 设置初始端口转发 ===");

                // 清理现有端口转发
                Console.WriteLine("1. 清理现有端口转发...");
                var removeResult = await RunAdbCommandAsync($"-s {deviceId} forward --remove-all");
                Console.WriteLine($"   清理结果: {(string.IsNullOrEmpty(removeResult.Trim()) ? "成功" : removeResult)}");

                await Task.Delay(500);

                // 设置基本的端口转发，让服务器能够监听
                Console.WriteLine("2. 设置基本端口转发...");
                var videoCommand = $"-s {deviceId} forward tcp:{ScrcpyProtocol.DEFAULT_SERVER_PORT} tcp:{ScrcpyProtocol.DEFAULT_SERVER_PORT}";
                Console.WriteLine($"   执行命令: adb {videoCommand}");
                var videoResult = await RunAdbCommandAsync(videoCommand);
                Console.WriteLine($"   返回结果: {(string.IsNullOrEmpty(videoResult.Trim()) ? "成功 (无输出)" : videoResult)}");

                var controlCommand = $"-s {deviceId} forward tcp:{ScrcpyProtocol.DEFAULT_CONTROL_PORT} tcp:{ScrcpyProtocol.DEFAULT_CONTROL_PORT}";
                Console.WriteLine($"   执行命令: adb {controlCommand}");
                var controlResult = await RunAdbCommandAsync(controlCommand);
                Console.WriteLine($"   返回结果: {(string.IsNullOrEmpty(controlResult.Trim()) ? "成功 (无输出)" : controlResult)}");

                bool success = !videoResult.ToLower().Contains("error") && !controlResult.ToLower().Contains("error");
                Console.WriteLine($"   状态: {(success ? "✅ 成功" : "❌ 失败")}");

                return success;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 初始端口转发设置异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 设置端口转发 (连接到scrcpy socket)
        /// </summary>
        private async Task<bool> SetupPortForwardingAsync(string deviceId)
        {
            try
            {
                Console.WriteLine("=== 验证端口转发 ===");

                // 检查端口转发是否已经设置
                Console.WriteLine("1. 检查当前端口转发...");
                var listCommand = $"-s {deviceId} forward --list";
                Console.WriteLine($"   执行命令: adb {listCommand}");
                var listResult = await RunAdbCommandAsync(listCommand);
                Console.WriteLine($"   当前转发列表:");
                if (string.IsNullOrEmpty(listResult.Trim()))
                {
                    Console.WriteLine("   (无端口转发)");
                    return false;
                }
                else
                {
                    foreach (var line in listResult.Split('\n'))
                    {
                        if (!string.IsNullOrWhiteSpace(line))
                        {
                            Console.WriteLine($"     {line.Trim()}");
                        }
                    }
                }

                // 检查是否包含我们需要的端口
                bool hasVideoPort = listResult.Contains($"tcp:{ScrcpyProtocol.DEFAULT_SERVER_PORT}");
                bool hasControlPort = listResult.Contains($"tcp:{ScrcpyProtocol.DEFAULT_CONTROL_PORT}");

                Console.WriteLine($"2. 端口转发状态:");
                Console.WriteLine($"   视频端口 ({ScrcpyProtocol.DEFAULT_SERVER_PORT}): {(hasVideoPort ? "✅ 已设置" : "❌ 未设置")}");
                Console.WriteLine($"   控制端口 ({ScrcpyProtocol.DEFAULT_CONTROL_PORT}): {(hasControlPort ? "✅ 已设置" : "❌ 未设置")}");

                bool success = hasVideoPort && hasControlPort;
                Console.WriteLine($"   总体状态: {(success ? "✅ 成功" : "❌ 失败")}");

                return success;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 端口转发验证异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 移除端口转发
        /// </summary>
        private async Task RemovePortForwardingAsync(string deviceId)
        {
            try
            {
                await RunAdbCommandAsync($"-s {deviceId} forward --remove tcp:{ScrcpyProtocol.DEFAULT_SERVER_PORT}");
                await RunAdbCommandAsync($"-s {deviceId} forward --remove tcp:{ScrcpyProtocol.DEFAULT_CONTROL_PORT}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to remove port forwarding: {ex.Message}");
            }
        }

        /// <summary>
        /// 启动scrcpy服务器
        /// </summary>
        private async Task<bool> StartScrcpyServerAsync(string deviceId, int maxSize, int bitRate)
        {
            try
            {
                Console.WriteLine("=== 启动Scrcpy服务器 ===");

                // 首先检查服务器文件是否存在
                Console.WriteLine("1. 检查服务器文件...");
                var checkResult = await RunAdbCommandAsync($"-s {deviceId} shell ls -l /data/local/tmp/scrcpy-server.jar");
                Console.WriteLine($"   文件检查结果: {checkResult}");

                if (checkResult.Contains("No such file") || checkResult.Contains("not found"))
                {
                    Console.WriteLine("   ❌ 服务器文件不存在，尝试推送...");
                    if (System.IO.File.Exists("scrcpy-server.jar"))
                    {
                        var pushResult = await RunAdbCommandAsync($"-s {deviceId} push scrcpy-server.jar /data/local/tmp/");
                        Console.WriteLine($"   推送结果: {pushResult}");
                    }
                    else
                    {
                        Console.WriteLine("   ❌ 本地scrcpy-server.jar文件不存在");
                        return false;
                    }
                }
                else
                {
                    Console.WriteLine("   ✅ 服务器文件存在");
                }

                // 停止可能运行的旧服务器进程
                Console.WriteLine("2. 停止现有服务器进程...");
                var killResult = await RunAdbCommandAsync($"-s {deviceId} shell pkill -f scrcpy");
                Console.WriteLine($"   停止结果: {(string.IsNullOrEmpty(killResult.Trim()) ? "完成" : killResult)}");

                await Task.Delay(1000);

                // 构建服务器启动命令 (使用新版本格式)
                Console.WriteLine("3. 构建启动命令...");

                // 生成唯一的scid (scrcpy connection id)
                var scid = DateTime.Now.Ticks.ToString("X8").Substring(0, 8);

                var serverArgs = $"video_bit_rate={bitRate} " +
                               $"log_level=info " +
                               $"max_size={maxSize} " +
                               $"capture_orientation=0 " +
                               $"audio=false ";

                var command = $"-s {deviceId} shell CLASSPATH=/data/local/tmp/scrcpy-server.jar app_process / com.genymobile.scrcpy.Server {ScrcpyProtocol.PROTOCOL_VERSION} {serverArgs}";

                Console.WriteLine($"   服务器参数: {serverArgs}");
                Console.WriteLine($"   完整命令: adb {command}");

                Console.WriteLine("4. 启动服务器进程...");
                _serverProcess = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = ScrcpyConfig.AdbPath,
                        Arguments = command,
                        UseShellExecute = false,
                        CreateNoWindow = true,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true
                    }
                };

                _serverProcess.Start();
                Console.WriteLine($"   进程ID: {_serverProcess.Id}");

                // 等待服务器启动并检查输出
                Console.WriteLine("5. 等待服务器启动...");
                await Task.Delay(3000);

                bool isRunning = _serverProcess != null && !_serverProcess.HasExited;
                Console.WriteLine($"   服务器状态: {(isRunning ? "✅ 运行中" : "❌ 已退出")}");

                if (!isRunning && _serverProcess != null)
                {
                    Console.WriteLine($"   退出代码: {_serverProcess.ExitCode}");

                    // 尝试读取错误输出
                    try
                    {
                        var errorOutput = await _serverProcess.StandardError.ReadToEndAsync();
                        if (!string.IsNullOrEmpty(errorOutput))
                        {
                            Console.WriteLine($"   错误输出: {errorOutput}");
                        }

                        var standardOutput = await _serverProcess.StandardOutput.ReadToEndAsync();
                        if (!string.IsNullOrEmpty(standardOutput))
                        {
                            Console.WriteLine($"   标准输出: {standardOutput}");
                        }
                    }
                    catch (Exception readEx)
                    {
                        Console.WriteLine($"   读取输出失败: {readEx.Message}");
                    }
                }

                return isRunning;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 启动服务器异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 连接视频流
        /// </summary>
        private async Task<bool> ConnectVideoStreamAsync()
        {
            try
            {
                Console.WriteLine("=== 连接视频流 ===");
                Console.WriteLine($"1. 尝试连接到 127.0.0.1:{ScrcpyProtocol.DEFAULT_SERVER_PORT}...");

                _videoClient = new TcpClient();

                // 设置连接超时
                var connectTask = _videoClient.ConnectAsync("127.0.0.1", ScrcpyProtocol.DEFAULT_SERVER_PORT);
                var timeoutTask = Task.Delay(ScrcpyConfig.ConnectionTimeout);

                var completedTask = await Task.WhenAny(connectTask, timeoutTask);

                if (completedTask == timeoutTask)
                {
                    Console.WriteLine($"   ❌ 连接超时 ({ScrcpyConfig.ConnectionTimeout}ms)");
                    _videoClient?.Close();
                    return false;
                }

                await connectTask; // 确保连接任务完成

                if (_videoClient.Connected)
                {
                    Console.WriteLine("   ✅ TCP连接建立成功");
                    _videoStream = _videoClient.GetStream();

                    // 测试流是否可读
                    Console.WriteLine("2. 测试视频流...");
                    if (_videoStream.CanRead)
                    {
                        Console.WriteLine("   ✅ 视频流可读");
                        Console.WriteLine("   ✅ 视频流连接成功");
                        return true;
                    }
                    else
                    {
                        Console.WriteLine("   ❌ 视频流不可读");
                        return false;
                    }
                }
                else
                {
                    Console.WriteLine("   ❌ TCP连接失败");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 视频流连接异常: {ex.Message}");
                Console.WriteLine($"   异常类型: {ex.GetType().Name}");

                if (ex.InnerException != null)
                {
                    Console.WriteLine($"   内部异常: {ex.InnerException.Message}");
                }

                return false;
            }
        }

        /// <summary>
        /// 接收视频数据
        /// </summary>
        private async Task ReceiveVideoDataAsync(CancellationToken cancellationToken)
        {
            var buffer = new byte[65536]; // 64KB缓冲区

            try
            {
                while (!cancellationToken.IsCancellationRequested && _videoStream != null)
                {
                    var bytesRead = await _videoStream.ReadAsync(buffer, 0, buffer.Length, cancellationToken);
                    if (bytesRead > 0)
                    {
                        // 解码视频数据
                        _videoDecoder?.DecodeFrame(buffer, bytesRead);
                    }
                    else
                    {
                        // 连接断开
                        break;
                    }
                }
            }
            catch (OperationCanceledException)
            {
                // 正常取消
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error receiving video data: {ex.Message}");
            }
        }

        /// <summary>
        /// 运行ADB命令
        /// </summary>
        private async Task<string> RunAdbCommandAsync(string arguments)
        {
            try
            {
                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = ScrcpyConfig.AdbPath,
                        Arguments = arguments,
                        UseShellExecute = false,
                        CreateNoWindow = true,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true
                    }
                };

                process.Start();
                var output = await process.StandardOutput.ReadToEndAsync();
                var error = await process.StandardError.ReadToEndAsync();
                process.WaitForExit();

                // 如果有错误输出，记录并返回错误信息
                if (!string.IsNullOrEmpty(error))
                {
                    Console.WriteLine($"ADB command error: {error}");
                    return $"ERROR: {error}";
                }

                return output;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to run adb command '{arguments}': {ex.Message}");
                return $"EXCEPTION: {ex.Message}";
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            DisconnectAsync().Wait();
        }
    }
}
