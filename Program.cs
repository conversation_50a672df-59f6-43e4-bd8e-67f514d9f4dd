using System;
using System.Threading.Tasks;
using System.Threading;

namespace AutoGame
{
    internal class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("AutoGame - Scrcpy Android Device Controller");
            Console.WriteLine("==========================================");

            // 加载配置
            ScrcpyConfig.LoadFromFile();

            // 验证配置
            var configResult = ScrcpyConfig.ValidateConfig();
            configResult.PrintResults();

            if (!configResult.IsValid)
            {
                Console.WriteLine("配置验证失败，请检查配置文件。");
                Console.WriteLine("按任意键退出...");
                Console.ReadKey();
                return;
            }

            var client = new ScrcpyClient();

            try
            {
                // 连接到安卓设备
                Console.WriteLine("Connecting to Android device...");
                bool connected = await client.ConnectAsync();

                if (!connected)
                {
                    Console.WriteLine("Failed to connect to device. Please ensure:");
                    Console.WriteLine("1. ADB is installed and in PATH");
                    Console.WriteLine("2. Android device is connected via USB");
                    Console.WriteLine("3. USB debugging is enabled");
                    Console.WriteLine("4. scrcpy-server.jar is pushed to /data/local/tmp/");
                    return;
                }

                Console.WriteLine($"Connected to device: {client.DeviceInfo}");
                Console.WriteLine("\nAvailable commands:");
                Console.WriteLine("1. 'click x y' - Click at coordinates (x, y)");
                Console.WriteLine("2. 'swipe x1 y1 x2 y2' - Swipe from (x1,y1) to (x2,y2)");
                Console.WriteLine("3. 'home' - Press home button");
                Console.WriteLine("4. 'back' - Press back button");
                Console.WriteLine("5. 'power' - Press power button");
                Console.WriteLine("6. 'screen on/off' - Turn screen on/off");
                Console.WriteLine("7. 'frame' - Get latest frame info");
                Console.WriteLine("8. 'quit' - Exit program");

                // 主循环
                while (true)
                {
                    Console.Write("\nEnter command: ");
                    var input = Console.ReadLine()?.Trim().ToLower();

                    if (string.IsNullOrEmpty(input))
                        continue;

                    var parts = input.Split(' ');
                    var command = parts[0];

                    try
                    {
                        switch (command)
                        {
                            case "quit":
                            case "exit":
                                goto exit;

                            case "click":
                                if (parts.Length >= 3 && int.TryParse(parts[1], out int x) && int.TryParse(parts[2], out int y))
                                {
                                    bool success = await client.DeviceController.ClickAsync(x, y);
                                    Console.WriteLine(success ? $"Clicked at ({x}, {y})" : "Click failed");
                                }
                                else
                                {
                                    Console.WriteLine("Usage: click x y");
                                }
                                break;

                            case "swipe":
                                if (parts.Length >= 5 &&
                                    int.TryParse(parts[1], out int x1) && int.TryParse(parts[2], out int y1) &&
                                    int.TryParse(parts[3], out int x2) && int.TryParse(parts[4], out int y2))
                                {
                                    bool success = await client.DeviceController.SwipeAsync(x1, y1, x2, y2);
                                    Console.WriteLine(success ? $"Swiped from ({x1}, {y1}) to ({x2}, {y2})" : "Swipe failed");
                                }
                                else
                                {
                                    Console.WriteLine("Usage: swipe x1 y1 x2 y2");
                                }
                                break;

                            case "home":
                                bool homeSuccess = await client.DeviceController.PressHomeAsync();
                                Console.WriteLine(homeSuccess ? "Home button pressed" : "Home button press failed");
                                break;

                            case "back":
                                bool backSuccess = await client.DeviceController.PressBackAsync();
                                Console.WriteLine(backSuccess ? "Back button pressed" : "Back button press failed");
                                break;

                            case "power":
                                bool powerSuccess = await client.DeviceController.PressPowerAsync();
                                Console.WriteLine(powerSuccess ? "Power button pressed" : "Power button press failed");
                                break;

                            case "screen":
                                if (parts.Length >= 2)
                                {
                                    bool screenOn = parts[1] == "on";
                                    bool screenSuccess = await client.DeviceController.SetScreenPowerAsync(screenOn);
                                    Console.WriteLine(screenSuccess ? $"Screen turned {(screenOn ? "on" : "off")}" : "Screen power change failed");
                                }
                                else
                                {
                                    Console.WriteLine("Usage: screen on/off");
                                }
                                break;

                            case "frame":
                                var frame = client.GetLatestFrame();
                                if (frame != null)
                                {
                                    Console.WriteLine($"Latest frame: {frame.Width}x{frame.Height}, Frame #{frame.FrameNumber}, Time: {frame.Timestamp}");
                                }
                                else
                                {
                                    Console.WriteLine("No frame available");
                                }
                                break;

                            default:
                                Console.WriteLine("Unknown command. Type 'quit' to exit.");
                                break;
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error executing command: {ex.Message}");
                    }
                }

                exit:
                Console.WriteLine("Disconnecting...");
                await client.DisconnectAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
            finally
            {
                client?.Dispose();
            }

            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}
