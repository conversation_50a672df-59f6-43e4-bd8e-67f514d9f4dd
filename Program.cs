using System;
using System.Threading.Tasks;
using System.Threading;

namespace AutoGame
{
    internal class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("AutoGame - Scrcpy Android Device Controller");
            Console.WriteLine("==========================================");

            // 检查命令行参数
            if (args.Length > 0)
            {
                switch (args[0].ToLower())
                {
                    case "--diagnostic":
                        await DiagnosticTool.RunFullDiagnosticAsync();
                        Console.WriteLine("\n按任意键退出...");
                        Console.ReadKey();
                        return;

                    case "--fix":
                        await QuickFix.RunFixWizardAsync();
                        return;

                    case "--autofix":
                        await QuickFix.AutoFixAsync();
                        Console.WriteLine("\n按任意键退出...");
                        Console.ReadKey();
                        return;

                    case "--help":
                        ShowHelp();
                        return;

                    case "--test":
                        await TestAdbCommands.RunAllTestsAsync();
                        Console.WriteLine("\n按任意键退出...");
                        Console.ReadKey();
                        return;

                    case "--test-interactive":
                        await TestAdbCommands.RunInteractiveTestAsync();
                        return;
                }
            }

            // 加载配置
            ScrcpyConfig.LoadFromFile();

            // 验证配置
            var configResult = ScrcpyConfig.ValidateConfig();
            configResult.PrintResults();

            if (!configResult.IsValid)
            {
                Console.WriteLine("配置验证失败，请检查配置文件。");
                Console.WriteLine("可用的修复选项:");
                Console.WriteLine("  AutoGame.exe --diagnostic  - 运行详细诊断");
                Console.WriteLine("  AutoGame.exe --fix         - 启动修复向导");
                Console.WriteLine("  AutoGame.exe --autofix     - 自动修复");
                Console.WriteLine("按任意键退出...");
                Console.ReadKey();
                return;
            }

            var client = new ScrcpyClient();

            try
            {
                // 连接到安卓设备
                Console.WriteLine("Connecting to Android device...");
                bool connected = await client.ConnectAsync();

                if (!connected)
                {
                    Console.WriteLine("设备连接失败。请检查:");
                    Console.WriteLine("1. ADB已安装并添加到PATH");
                    Console.WriteLine("2. Android设备已通过USB连接");
                    Console.WriteLine("3. USB调试已启用");
                    Console.WriteLine("4. scrcpy-server.jar已推送到设备");
                    Console.WriteLine();
                    Console.WriteLine("故障排除选项:");
                    Console.WriteLine("  AutoGame.exe --diagnostic  - 运行详细诊断");
                    Console.WriteLine("  AutoGame.exe --fix         - 启动修复向导");
                    Console.WriteLine("  AutoGame.exe --autofix     - 自动修复常见问题");
                    return;
                }

                Console.WriteLine($"Connected to device: {client.DeviceInfo}");
                Console.WriteLine("\nAvailable commands:");
                Console.WriteLine("1. 'click x y' - Click at coordinates (x, y)");
                Console.WriteLine("2. 'swipe x1 y1 x2 y2' - Swipe from (x1,y1) to (x2,y2)");
                Console.WriteLine("3. 'home' - Press home button");
                Console.WriteLine("4. 'back' - Press back button");
                Console.WriteLine("5. 'power' - Press power button");
                Console.WriteLine("6. 'screen on/off' - Turn screen on/off");
                Console.WriteLine("7. 'frame' - Get latest frame info");
                Console.WriteLine("8. 'diagnostic' - Run diagnostic check");
                Console.WriteLine("9. 'quit' - Exit program");

                // 主循环
                while (true)
                {
                    Console.Write("\nEnter command: ");
                    var input = Console.ReadLine()?.Trim().ToLower();

                    if (string.IsNullOrEmpty(input))
                        continue;

                    var parts = input.Split(' ');
                    var command = parts[0];

                    try
                    {
                        switch (command)
                        {
                            case "quit":
                            case "exit":
                                goto exit;

                            case "click":
                                if (parts.Length >= 3 && int.TryParse(parts[1], out int x) && int.TryParse(parts[2], out int y))
                                {
                                    bool success = await client.DeviceController.ClickAsync(x, y);
                                    Console.WriteLine(success ? $"Clicked at ({x}, {y})" : "Click failed");
                                }
                                else
                                {
                                    Console.WriteLine("Usage: click x y");
                                }
                                break;

                            case "swipe":
                                if (parts.Length >= 5 &&
                                    int.TryParse(parts[1], out int x1) && int.TryParse(parts[2], out int y1) &&
                                    int.TryParse(parts[3], out int x2) && int.TryParse(parts[4], out int y2))
                                {
                                    bool success = await client.DeviceController.SwipeAsync(x1, y1, x2, y2);
                                    Console.WriteLine(success ? $"Swiped from ({x1}, {y1}) to ({x2}, {y2})" : "Swipe failed");
                                }
                                else
                                {
                                    Console.WriteLine("Usage: swipe x1 y1 x2 y2");
                                }
                                break;

                            case "home":
                                bool homeSuccess = await client.DeviceController.PressHomeAsync();
                                Console.WriteLine(homeSuccess ? "Home button pressed" : "Home button press failed");
                                break;

                            case "back":
                                bool backSuccess = await client.DeviceController.PressBackAsync();
                                Console.WriteLine(backSuccess ? "Back button pressed" : "Back button press failed");
                                break;

                            case "power":
                                bool powerSuccess = await client.DeviceController.PressPowerAsync();
                                Console.WriteLine(powerSuccess ? "Power button pressed" : "Power button press failed");
                                break;

                            case "screen":
                                if (parts.Length >= 2)
                                {
                                    bool screenOn = parts[1] == "on";
                                    bool screenSuccess = await client.DeviceController.SetScreenPowerAsync(screenOn);
                                    Console.WriteLine(screenSuccess ? $"Screen turned {(screenOn ? "on" : "off")}" : "Screen power change failed");
                                }
                                else
                                {
                                    Console.WriteLine("Usage: screen on/off");
                                }
                                break;

                            case "frame":
                                var frame = client.GetLatestFrame();
                                if (frame != null)
                                {
                                    Console.WriteLine($"Latest frame: {frame.Width}x{frame.Height}, Frame #{frame.FrameNumber}, Time: {frame.Timestamp}");
                                }
                                else
                                {
                                    Console.WriteLine("No frame available");
                                }
                                break;

                            case "diagnostic":
                                Console.WriteLine("Running diagnostic check...");
                                await DiagnosticTool.RunFullDiagnosticAsync();
                                break;

                            default:
                                Console.WriteLine("Unknown command. Type 'quit' to exit.");
                                break;
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error executing command: {ex.Message}");
                    }
                }

                exit:
                Console.WriteLine("Disconnecting...");
                await client.DisconnectAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
            finally
            {
                client?.Dispose();
            }

            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }

        /// <summary>
        /// 显示帮助信息
        /// </summary>
        static void ShowHelp()
        {
            Console.WriteLine("AutoGame - Scrcpy Android Device Controller");
            Console.WriteLine("==========================================");
            Console.WriteLine();
            Console.WriteLine("用法:");
            Console.WriteLine("  AutoGame.exe                 - 启动交互式界面");
            Console.WriteLine("  AutoGame.exe --diagnostic    - 运行诊断检查");
            Console.WriteLine("  AutoGame.exe --fix           - 启动修复向导");
            Console.WriteLine("  AutoGame.exe --autofix       - 自动修复常见问题");
            Console.WriteLine("  AutoGame.exe --test          - 运行ADB命令测试");
            Console.WriteLine("  AutoGame.exe --test-interactive - 交互式ADB测试");
            Console.WriteLine("  AutoGame.exe --help          - 显示此帮助信息");
            Console.WriteLine();
            Console.WriteLine("交互式命令:");
            Console.WriteLine("  click x y                    - 点击坐标(x, y)");
            Console.WriteLine("  swipe x1 y1 x2 y2           - 从(x1,y1)滑动到(x2,y2)");
            Console.WriteLine("  home                         - 按下Home键");
            Console.WriteLine("  back                         - 按下返回键");
            Console.WriteLine("  power                        - 按下电源键");
            Console.WriteLine("  screen on/off                - 开启/关闭屏幕");
            Console.WriteLine("  frame                        - 获取最新帧信息");
            Console.WriteLine("  diagnostic                   - 运行诊断检查");
            Console.WriteLine("  quit                         - 退出程序");
            Console.WriteLine();
            Console.WriteLine("故障排除:");
            Console.WriteLine("  如果连接失败，请尝试:");
            Console.WriteLine("  1. 运行 'AutoGame.exe --diagnostic' 检查问题");
            Console.WriteLine("  2. 运行 'AutoGame.exe --fix' 使用修复向导");
            Console.WriteLine("  3. 确保USB调试已启用");
            Console.WriteLine("  4. 确保scrcpy-server.jar已推送到设备");
            Console.WriteLine();
        }
    }
}
