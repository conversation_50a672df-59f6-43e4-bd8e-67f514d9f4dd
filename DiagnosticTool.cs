using System;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;

namespace AutoGame
{
    /// <summary>
    /// 诊断工具类，用于检查环境和连接问题
    /// </summary>
    public static class DiagnosticTool
    {
        /// <summary>
        /// 运行完整的诊断检查
        /// </summary>
        public static async Task RunFullDiagnosticAsync()
        {
            Console.WriteLine("=== AutoGame 诊断工具 ===");
            Console.WriteLine();

            await CheckAdbAsync();
            await CheckDeviceConnectionAsync();
            await CheckScrcpyServerAsync();
            await CheckPortsAsync();
            CheckFFmpegLibraries();
            
            Console.WriteLine();
            Console.WriteLine("=== 诊断完成 ===");
        }

        /// <summary>
        /// 检查ADB
        /// </summary>
        public static async Task CheckAdbAsync()
        {
            Console.WriteLine("1. 检查ADB...");
            
            try
            {
                var result = await RunCommandAsync("adb", "version");
                if (result.Contains("Android Debug Bridge"))
                {
                    Console.WriteLine("   ✅ ADB已安装");
                    Console.WriteLine($"   版本信息: {result.Split('\n')[0]}");
                }
                else
                {
                    Console.WriteLine("   ❌ ADB未正确安装");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ ADB检查失败: {ex.Message}");
                Console.WriteLine("   请确保ADB已安装并添加到PATH环境变量");
            }
        }

        /// <summary>
        /// 检查设备连接
        /// </summary>
        public static async Task CheckDeviceConnectionAsync()
        {
            Console.WriteLine("\n2. 检查设备连接...");
            
            try
            {
                var result = await RunCommandAsync("adb", "devices");
                Console.WriteLine($"   设备列表:\n{result}");
                
                if (result.Contains("\tdevice"))
                {
                    Console.WriteLine("   ✅ 检测到已连接的设备");
                    
                    // 获取设备详细信息
                    await GetDeviceDetailsAsync();
                }
                else
                {
                    Console.WriteLine("   ❌ 未检测到设备");
                    Console.WriteLine("   请检查:");
                    Console.WriteLine("   - USB连接是否正常");
                    Console.WriteLine("   - 是否启用了USB调试");
                    Console.WriteLine("   - 是否授权了此计算机");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 设备检查失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取设备详细信息
        /// </summary>
        private static async Task GetDeviceDetailsAsync()
        {
            try
            {
                var model = await RunCommandAsync("adb", "shell getprop ro.product.model");
                var version = await RunCommandAsync("adb", "shell getprop ro.build.version.release");
                var api = await RunCommandAsync("adb", "shell getprop ro.build.version.sdk");
                var size = await RunCommandAsync("adb", "shell wm size");
                
                Console.WriteLine($"   设备型号: {model.Trim()}");
                Console.WriteLine($"   Android版本: {version.Trim()}");
                Console.WriteLine($"   API级别: {api.Trim()}");
                Console.WriteLine($"   屏幕尺寸: {size.Trim()}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   获取设备信息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查scrcpy-server.jar
        /// </summary>
        public static async Task CheckScrcpyServerAsync()
        {
            Console.WriteLine("\n3. 检查scrcpy-server.jar...");
            
            // 检查本地文件
            if (File.Exists("scrcpy-server.jar"))
            {
                var fileInfo = new FileInfo("scrcpy-server.jar");
                Console.WriteLine($"   ✅ 本地文件存在 (大小: {fileInfo.Length} 字节)");
            }
            else
            {
                Console.WriteLine("   ❌ 本地scrcpy-server.jar文件不存在");
                Console.WriteLine("   请从 https://github.com/Genymobile/scrcpy/releases 下载");
                return;
            }
            
            // 检查设备上的文件
            try
            {
                var result = await RunCommandAsync("adb", "shell ls -l /data/local/tmp/scrcpy-server.jar");
                if (result.Contains("scrcpy-server.jar"))
                {
                    Console.WriteLine("   ✅ 设备上的服务器文件存在");
                    Console.WriteLine($"   文件信息: {result.Trim()}");
                }
                else
                {
                    Console.WriteLine("   ❌ 设备上的服务器文件不存在");
                    Console.WriteLine("   正在推送文件到设备...");
                    
                    var pushResult = await RunCommandAsync("adb", "push scrcpy-server.jar /data/local/tmp/");
                    if (pushResult.Contains("1 file pushed"))
                    {
                        Console.WriteLine("   ✅ 文件推送成功");
                    }
                    else
                    {
                        Console.WriteLine($"   ❌ 文件推送失败: {pushResult}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   检查设备文件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查端口占用情况
        /// </summary>
        public static async Task CheckPortsAsync()
        {
            Console.WriteLine("\n4. 检查端口占用...");
            
            try
            {
                var netstatResult = await RunCommandAsync("netstat", "-an");
                
                bool port8080Used = netstatResult.Contains(":8080");
                bool port8081Used = netstatResult.Contains(":8081");
                
                Console.WriteLine($"   端口8080 (视频): {(port8080Used ? "❌ 已占用" : "✅ 可用")}");
                Console.WriteLine($"   端口8081 (控制): {(port8081Used ? "❌ 已占用" : "✅ 可用")}");
                
                if (port8080Used || port8081Used)
                {
                    Console.WriteLine("   建议关闭占用端口的程序或重启计算机");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   端口检查失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查FFmpeg库文件
        /// </summary>
        public static void CheckFFmpegLibraries()
        {
            Console.WriteLine("\n5. 检查FFmpeg库文件...");
            
            string[] requiredFiles = {
                "avcodec-60.dll",
                "avformat-60.dll",
                "avutil-58.dll",
                "swresample-4.dll",
                "swscale-7.dll"
            };
            
            string ffmpegPath = ScrcpyConfig.FFmpegPath;
            
            if (!Directory.Exists(ffmpegPath))
            {
                Console.WriteLine($"   ❌ FFmpeg目录不存在: {ffmpegPath}");
                return;
            }
            
            Console.WriteLine($"   FFmpeg路径: {ffmpegPath}");
            
            bool allFilesExist = true;
            foreach (var file in requiredFiles)
            {
                var filePath = Path.Combine(ffmpegPath, file);
                if (File.Exists(filePath))
                {
                    var fileInfo = new FileInfo(filePath);
                    Console.WriteLine($"   ✅ {file} (大小: {fileInfo.Length} 字节)");
                }
                else
                {
                    Console.WriteLine($"   ❌ {file} 缺失");
                    allFilesExist = false;
                }
            }
            
            if (!allFilesExist)
            {
                Console.WriteLine("   请从 https://github.com/BtbN/FFmpeg-Builds/releases 下载FFmpeg库文件");
            }
        }

        /// <summary>
        /// 测试端口转发
        /// </summary>
        public static async Task TestPortForwardingAsync(string deviceId)
        {
            Console.WriteLine("\n6. 测试端口转发...");
            
            try
            {
                // 清理现有转发
                await RunCommandAsync("adb", $"-s {deviceId} forward --remove-all");
                
                // 设置转发
                var videoResult = await RunCommandAsync("adb", $"-s {deviceId} forward tcp:8080 localabstract:scrcpy");
                var controlResult = await RunCommandAsync("adb", $"-s {deviceId} forward tcp:8081 localabstract:scrcpy_control");
                
                Console.WriteLine($"   视频端口转发: {(string.IsNullOrEmpty(videoResult) ? "✅ 成功" : $"❌ {videoResult}")}");
                Console.WriteLine($"   控制端口转发: {(string.IsNullOrEmpty(controlResult) ? "✅ 成功" : $"❌ {controlResult}")}");
                
                // 列出当前转发
                var listResult = await RunCommandAsync("adb", $"-s {deviceId} forward --list");
                Console.WriteLine($"   当前转发列表:\n{listResult}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   端口转发测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 运行命令并返回输出
        /// </summary>
        private static async Task<string> RunCommandAsync(string fileName, string arguments)
        {
            var process = new Process
            {
                StartInfo = new ProcessStartInfo
                {
                    FileName = fileName,
                    Arguments = arguments,
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                }
            };

            process.Start();
            var output = await process.StandardOutput.ReadToEndAsync();
            var error = await process.StandardError.ReadToEndAsync();
            process.WaitForExit();

            return string.IsNullOrEmpty(error) ? output : $"ERROR: {error}";
        }

        /// <summary>
        /// 生成诊断报告
        /// </summary>
        public static async Task GenerateReportAsync()
        {
            var reportPath = "diagnostic_report.txt";
            using (var writer = new StreamWriter(reportPath))
            {
                writer.WriteLine("AutoGame 诊断报告");
                writer.WriteLine($"生成时间: {DateTime.Now}");
                writer.WriteLine(new string('=', 50));
                
                // 重定向控制台输出到文件
                var originalOut = Console.Out;
                Console.SetOut(writer);
                
                await RunFullDiagnosticAsync();
                
                Console.SetOut(originalOut);
            }
            
            Console.WriteLine($"诊断报告已保存到: {reportPath}");
        }
    }
}
