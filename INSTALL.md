# AutoGame 安装指南

## 系统要求

- Windows 10/11
- .NET Framework 4.7.2 或更高版本
- Visual Studio 2019 或更高版本（用于编译）
- Android设备（支持USB调试）

## 安装步骤

### 1. 安装ADB (Android Debug Bridge)

#### 方法一：通过Android SDK Platform Tools
1. 下载 [Android SDK Platform Tools](https://developer.android.com/studio/releases/platform-tools)
2. 解压到任意目录（如 `C:\adb`）
3. 将ADB目录添加到系统PATH环境变量

#### 方法二：通过Android Studio
1. 安装Android Studio
2. 在SDK Manager中安装Android SDK Platform Tools
3. 将 `%ANDROID_HOME%\platform-tools` 添加到PATH

#### 验证ADB安装
打开命令提示符，运行：
```cmd
adb version
```

### 2. 下载FFmpeg库文件

1. 访问 [FFmpeg Builds](https://github.com/BtbN/FFmpeg-Builds/releases)
2. 下载 `ffmpeg-master-latest-win64-gpl-shared.zip`
3. 解压后，将以下DLL文件复制到项目的 `ffmpeg` 目录：
   - `avcodec-60.dll`
   - `avformat-60.dll`
   - `avutil-58.dll`
   - `swresample-4.dll`
   - `swscale-7.dll`

### 3. 获取scrcpy-server.jar

1. 访问 [Scrcpy Releases](https://github.com/Genymobile/scrcpy/releases)
2. 下载最新版本的scrcpy
3. 从下载的文件中提取 `scrcpy-server.jar`
4. 将文件放在项目根目录

### 4. 设置Android设备

1. 在Android设备上启用开发者选项：
   - 进入 设置 > 关于手机
   - 连续点击"版本号"7次
   
2. 启用USB调试：
   - 进入 设置 > 开发者选项
   - 开启"USB调试"
   
3. 连接设备到电脑并授权调试

### 5. 推送服务器文件到设备

打开命令提示符，在项目目录运行：
```cmd
adb push scrcpy-server.jar /data/local/tmp/
```

### 6. 编译项目

1. 打开Visual Studio
2. 打开 `AutoGame.sln`
3. 选择 Debug 或 Release 配置
4. 按 F6 或选择 生成 > 生成解决方案

## 目录结构

编译完成后，项目目录应该如下所示：

```
AutoGame/
├── ffmpeg/
│   ├── avcodec-60.dll
│   ├── avformat-60.dll
│   ├── avutil-58.dll
│   ├── swresample-4.dll
│   └── swscale-7.dll
├── bin/Debug/
│   └── AutoGame.exe
├── scrcpy-server.jar
├── scrcpy.config
└── ...其他项目文件
```

## 运行程序

1. 确保Android设备已连接并启用USB调试
2. 运行 `AutoGame.exe`
3. 程序会自动验证配置并尝试连接设备

## 故障排除

### 连接失败

**问题**: 无法连接到Android设备
**解决方案**:
1. 检查USB连接
2. 确认USB调试已启用
3. 运行 `adb devices` 确认设备可见
4. 重新授权USB调试

### FFmpeg错误

**问题**: FFmpeg库加载失败
**解决方案**:
1. 确认所有DLL文件都在 `ffmpeg` 目录中
2. 检查DLL文件版本是否匹配
3. 确认系统架构（x64）

### 服务器启动失败

**问题**: scrcpy-server启动失败
**解决方案**:
1. 确认 `scrcpy-server.jar` 已推送到设备
2. 检查设备权限设置
3. 尝试重新推送服务器文件

### 视频解码问题

**问题**: 无法解码视频流
**解决方案**:
1. 检查设备是否支持H.264编码
2. 降低视频质量设置
3. 检查网络连接稳定性

## 配置文件

程序首次运行时会生成 `scrcpy.config` 配置文件，可以根据需要修改：

```ini
# FFmpeg库路径
FFmpegPath=ffmpeg

# ADB可执行文件路径
AdbPath=adb

# 默认最大分辨率
DefaultMaxSize=1920

# 默认比特率
DefaultBitRate=8000000
```

## 支持的设备

- Android 5.0 (API 21) 或更高版本
- 支持H.264硬件编码的设备
- 已root设备可能需要额外配置

## 性能优化

1. 降低分辨率以提高性能
2. 调整比特率平衡质量和性能
3. 使用USB 3.0连接以获得更好的传输速度
4. 关闭不必要的后台应用

## 许可证

本项目基于MIT许可证开源，使用的第三方库：
- FFmpeg.AutoGen (LGPL)
- OpenCvSharp (Apache 2.0)
- Scrcpy (Apache 2.0)
