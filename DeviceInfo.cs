using System;

namespace AutoGame
{
    /// <summary>
    /// 设备信息
    /// </summary>
    public class DeviceInfo
    {
        /// <summary>
        /// 设备名称
        /// </summary>
        public string DeviceName { get; set; }

        /// <summary>
        /// 设备ID
        /// </summary>
        public string DeviceId { get; set; }

        /// <summary>
        /// 屏幕宽度
        /// </summary>
        public int ScreenWidth { get; set; }

        /// <summary>
        /// 屏幕高度
        /// </summary>
        public int ScreenHeight { get; set; }

        /// <summary>
        /// 屏幕密度
        /// </summary>
        public int ScreenDensity { get; set; }

        /// <summary>
        /// 屏幕旋转角度
        /// </summary>
        public int Rotation { get; set; }

        /// <summary>
        /// Android版本
        /// </summary>
        public string AndroidVersion { get; set; }

        /// <summary>
        /// API级别
        /// </summary>
        public int ApiLevel { get; set; }

        /// <summary>
        /// 是否已连接
        /// </summary>
        public bool IsConnected { get; set; }

        public DeviceInfo()
        {
            IsConnected = false;
        }

        public override string ToString()
        {
            return $"Device: {DeviceName} ({DeviceId}) - {ScreenWidth}x{ScreenHeight} - Android {AndroidVersion} (API {ApiLevel})";
        }
    }
}
