# AutoGame - Scrcpy Android Device Controller

这是一个基于Scrcpy的Android设备控制库，使用FFmpeg.AutoGen解码视频流，提供实时屏幕镜像和设备控制功能。

## 功能特性

- 🔗 **设备连接**: 通过ADB连接Android设备
- 📺 **视频流**: 实时接收和解码H.264视频流
- 🎮 **设备控制**: 支持点击、滑动、按键等操作
- 🖼️ **帧获取**: 提供获取最新帧图像的方法
- ⚡ **高性能**: 使用FFmpeg硬件加速解码

## 系统要求

- .NET Framework 4.7.2 或更高版本
- Windows 操作系统
- ADB (Android Debug Bridge)
- FFmpeg 库文件
- 支持USB调试的Android设备

## 安装和配置

### 1. 安装ADB
确保ADB已安装并添加到系统PATH环境变量中。

### 2. 下载FFmpeg库
下载FFmpeg库文件并放置在项目的`ffmpeg`文件夹中：
```
AutoGame/
├── ffmpeg/
│   ├── avcodec-60.dll
│   ├── avformat-60.dll
│   ├── avutil-58.dll
│   ├── swresample-4.dll
│   └── swscale-7.dll
```

### 3. 准备scrcpy-server.jar
下载scrcpy-server.jar文件并推送到Android设备：
```bash
adb push scrcpy-server.jar /data/local/tmp/
```

### 4. 启用USB调试
在Android设备上启用开发者选项和USB调试。

## 使用方法

### 基本用法

```csharp
using AutoGame;

// 创建客户端
var client = new ScrcpyClient();

// 连接设备
bool connected = await client.ConnectAsync();

if (connected)
{
    // 获取最新帧
    var frame = client.GetLatestFrame();
    
    // 点击屏幕
    await client.DeviceController.ClickAsync(100, 200);
    
    // 滑动操作
    await client.DeviceController.SwipeAsync(100, 200, 300, 400);
    
    // 按下Home键
    await client.DeviceController.PressHomeAsync();
    
    // 息屏
    await client.DeviceController.SetScreenPowerAsync(false);
}

// 断开连接
await client.DisconnectAsync();
client.Dispose();
```

### 获取视频帧

```csharp
// 获取最新帧数据
var frameData = client.GetLatestFrame();
if (frameData != null)
{
    Console.WriteLine($"Frame: {frameData.Width}x{frameData.Height}");
    Console.WriteLine($"Frame Number: {frameData.FrameNumber}");
    
    // 转换为Bitmap
    var bitmap = frameData.ToBitmap();
    
    // 保存图片
    bitmap.Save("screenshot.png");
}
```

### 设备控制操作

```csharp
var controller = client.DeviceController;

// 点击操作
await controller.ClickAsync(x, y);                    // 普通点击
await controller.LongPressAsync(x, y, 1000);          // 长按1秒

// 滑动操作
await controller.SwipeAsync(x1, y1, x2, y2, 500);     // 滑动500ms

// 物理按键
await controller.PressHomeAsync();                    // Home键
await controller.PressBackAsync();                    // 返回键
await controller.PressPowerAsync();                   // 电源键
await controller.PressMenuAsync();                    // 菜单键
await controller.PressAppSwitchAsync();               // 最近任务键

// 屏幕控制
await controller.SetScreenPowerAsync(false);          // 息屏
await controller.SetScreenPowerAsync(true);           // 亮屏
```

## 核心类说明

### ScrcpyClient
主要的客户端类，负责设备连接和管理。

**主要方法:**
- `ConnectAsync()`: 连接设备
- `DisconnectAsync()`: 断开连接
- `GetLatestFrame()`: 获取最新帧

### VideoDecoder
视频解码器，使用FFmpeg.AutoGen解码H.264视频流。

**主要属性:**
- `LatestFrame`: 最新解码的帧
- `IsInitialized`: 是否已初始化
- `Width/Height`: 视频尺寸

### DeviceController
设备控制器，提供各种设备操作功能。

**主要方法:**
- `ClickAsync()`: 点击
- `SwipeAsync()`: 滑动
- `LongPressAsync()`: 长按
- `PressKeyAsync()`: 按键
- `SetScreenPowerAsync()`: 屏幕电源控制

### FrameData
帧数据结构，存储解码后的图像数据。

**主要属性:**
- `Width/Height`: 图像尺寸
- `RgbData`: RGB数据
- `FrameNumber`: 帧序号
- `Timestamp`: 时间戳

## 命令行界面

运行程序后，可以使用以下命令：

- `click x y` - 点击坐标(x, y)
- `swipe x1 y1 x2 y2` - 从(x1,y1)滑动到(x2,y2)
- `home` - 按下Home键
- `back` - 按下返回键
- `power` - 按下电源键
- `screen on/off` - 开启/关闭屏幕
- `frame` - 显示最新帧信息
- `quit` - 退出程序

## 故障排除

### 连接失败
1. 确认ADB已正确安装
2. 检查USB调试是否启用
3. 确认设备已通过USB连接
4. 检查scrcpy-server.jar是否已推送到设备

### 视频解码失败
1. 确认FFmpeg库文件路径正确
2. 检查FFmpeg版本兼容性
3. 确认设备支持H.264编码

### 控制操作无响应
1. 检查控制端口连接状态
2. 确认设备权限设置
3. 检查屏幕是否处于锁定状态

## 项目结构

```
AutoGame/
├── ScrcpyClient.cs          # 主客户端类
├── VideoDecoder.cs          # FFmpeg视频解码器
├── DeviceController.cs      # 设备控制器
├── ScrcpyProtocol.cs        # Scrcpy协议定义
├── FrameData.cs             # 帧数据结构
├── TouchEvent.cs            # 触摸事件定义
├── DeviceInfo.cs            # 设备信息
├── ScrcpyConfig.cs          # 配置管理
├── GameAutomationExample.cs # 游戏自动化示例
├── Program.cs               # 主程序入口
├── README.md                # 项目说明
├── INSTALL.md               # 安装指南
└── USAGE_EXAMPLES.md        # 使用示例
```

## 快速开始

1. **安装依赖**
   ```bash
   # 确保ADB已安装
   adb version
   ```

2. **下载FFmpeg库**
   - 从 [FFmpeg Builds](https://github.com/BtbN/FFmpeg-Builds/releases) 下载
   - 解压DLL文件到 `ffmpeg/` 目录

3. **获取scrcpy-server.jar**
   - 从 [Scrcpy Releases](https://github.com/Genymobile/scrcpy/releases) 下载
   - 推送到设备: `adb push scrcpy-server.jar /data/local/tmp/`

4. **编译运行**
   ```bash
   # 在Visual Studio中打开AutoGame.sln
   # 按F5运行或F6编译
   ```

## API参考

### ScrcpyClient
```csharp
// 连接设备
await client.ConnectAsync(deviceId, maxSize, bitRate);

// 获取最新帧
var frame = client.GetLatestFrame();

// 断开连接
await client.DisconnectAsync();
```

### DeviceController
```csharp
// 点击操作
await controller.ClickAsync(x, y);
await controller.LongPressAsync(x, y, duration);

// 滑动操作
await controller.SwipeAsync(x1, y1, x2, y2, duration);

// 物理按键
await controller.PressHomeAsync();
await controller.PressBackAsync();
await controller.PressPowerAsync();

// 屏幕控制
await controller.SetScreenPowerAsync(false); // 息屏
```

### VideoDecoder
```csharp
// 初始化解码器
decoder.Initialize();

// 解码帧数据
decoder.DecodeFrame(data, length);

// 获取最新帧
var frame = decoder.LatestFrame;
```

## 性能优化建议

1. **降低分辨率**: 设置较低的maxSize参数
2. **调整比特率**: 根据网络情况调整bitRate
3. **减少帧率**: 降低检查频率以节省CPU
4. **内存管理**: 及时释放Bitmap对象

## 兼容性

- **Android版本**: Android 5.0 (API 21) 及以上
- **设备要求**: 支持H.264硬件编码
- **Windows版本**: Windows 10/11
- **.NET版本**: .NET Framework 4.7.2+

## 许可证

本项目基于MIT许可证开源。

使用的第三方库：
- **FFmpeg.AutoGen**: LGPL许可证
- **OpenCvSharp**: Apache 2.0许可证
- **Scrcpy**: Apache 2.0许可证

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

### 贡献指南
1. Fork本项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 支持

如果您遇到问题或有建议，请：
1. 查看 [INSTALL.md](INSTALL.md) 安装指南
2. 查看 [USAGE_EXAMPLES.md](USAGE_EXAMPLES.md) 使用示例
3. 在GitHub上创建Issue
4. 查看已有的Issue和解决方案

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本的设备连接和控制
- 实现FFmpeg视频解码
- 提供完整的API接口
